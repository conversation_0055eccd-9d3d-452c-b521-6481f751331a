<?php
/**
 * Admin Settings Page
 * MLM Binary Plan System
 */

require_once '../includes/header.php';
require_once '../includes/Auth.php';
require_once '../config/config.php';

// Require admin authentication
Auth::requireAdmin();

$currentUser = Auth::user();
$adminId = Auth::id();

// Get database instance
$db = Database::getInstance();
$config = Config::getInstance();

$success = '';
$error = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    verifyCsrfToken();
    
    $action = $_POST['action'] ?? '';
    
    try {
        if ($action === 'update_mlm_settings') {
            // Update MLM settings
            $pvRate = floatval($_POST['pv_rate']);
            $dailyCapping = floatval($_POST['daily_capping']);
            $minWithdrawal = floatval($_POST['min_withdrawal']);
            
            $config->set('pv_rate', $pvRate);
            $config->set('daily_capping', $dailyCapping);
            $config->set('min_withdrawal', $minWithdrawal);
            
            $success = "MLM settings updated successfully!";
            
        } elseif ($action === 'update_company_info') {
            // Update company information
            $companyName = trim($_POST['company_name']);
            $supportEmail = trim($_POST['support_email']);
            $supportPhone = trim($_POST['support_phone']);
            
            $config->set('company_name', $companyName);
            $config->set('support_email', $supportEmail);
            $config->set('support_phone', $supportPhone);
            
            $success = "Company information updated successfully!";
            
        } elseif ($action === 'update_payment_settings') {
            // Update payment settings
            $razorpayMode = $_POST['razorpay_mode'];
            $razorpayKeyId = trim($_POST['razorpay_key_id']);
            $razorpayKeySecret = trim($_POST['razorpay_key_secret']);
            
            $config->set('razorpay_mode', $razorpayMode);
            $config->set('razorpay_key_id', $razorpayKeyId);
            $config->set('razorpay_key_secret', $razorpayKeySecret);
            
            $success = "Payment settings updated successfully!";
            
        } elseif ($action === 'change_password') {
            // Change admin password
            $currentPassword = $_POST['current_password'];
            $newPassword = $_POST['new_password'];
            $confirmPassword = $_POST['confirm_password'];
            
            // Verify current password
            $stmt = $db->prepare("SELECT password FROM admin WHERE id = ?");
            $stmt->execute([$adminId]);
            $admin = $stmt->fetch();
            
            if (!password_verify($currentPassword, $admin['password'])) {
                throw new Exception("Current password is incorrect.");
            }
            
            if ($newPassword !== $confirmPassword) {
                throw new Exception("New passwords do not match.");
            }
            
            if (strlen($newPassword) < 6) {
                throw new Exception("New password must be at least 6 characters long.");
            }
            
            // Update password
            $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
            $updateStmt = $db->prepare("UPDATE admin SET password = ? WHERE id = ?");
            $updateStmt->execute([$hashedPassword, $adminId]);
            
            $success = "Password changed successfully!";
        }
        
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

// Get current settings
$currentSettings = [
    'pv_rate' => $config->get('pv_rate', '0.10'),
    'daily_capping' => $config->get('daily_capping', '130000.00'),
    'min_withdrawal' => $config->get('min_withdrawal', '500.00'),
    'company_name' => $config->get('company_name', 'ShaktiPure MLM'),
    'support_email' => $config->get('support_email', '<EMAIL>'),
    'support_phone' => $config->get('support_phone', '+91-9999999999'),
    'razorpay_mode' => $config->get('razorpay_mode', 'test'),
    'razorpay_key_id' => $config->get('razorpay_key_id', ''),
    'razorpay_key_secret' => $config->get('razorpay_key_secret', '')
];
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Settings - <?php echo SITE_NAME; ?> Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
</head>
<body>
    <?php include 'includes/navigation.php'; ?>

    <div class="container-fluid mt-4">
        <!-- Flash Messages -->
        <?php if ($success): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle me-2"></i><?php echo htmlspecialchars($success); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>
        
        <?php if ($error): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-circle me-2"></i><?php echo htmlspecialchars($error); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>
        
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <h2><i class="fas fa-cog me-2"></i>System Settings</h2>
                <p class="text-muted">Configure system parameters and preferences</p>
            </div>
        </div>

        <div class="row">
            <!-- MLM Settings -->
            <div class="col-md-6">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-network-wired me-2"></i>MLM Settings</h5>
                    </div>
                    <div class="card-body">
                        <form method="POST">
                            <?php echo generateCsrfToken(); ?>
                            <input type="hidden" name="action" value="update_mlm_settings">
                            
                            <div class="mb-3">
                                <label for="pv_rate" class="form-label">PV Rate (₹ per PV)</label>
                                <input type="number" class="form-control" id="pv_rate" name="pv_rate" 
                                       value="<?php echo htmlspecialchars($currentSettings['pv_rate']); ?>" 
                                       step="0.01" min="0" required>
                                <div class="form-text">Amount in rupees for each PV point</div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="daily_capping" class="form-label">Daily Capping (₹)</label>
                                <input type="number" class="form-control" id="daily_capping" name="daily_capping" 
                                       value="<?php echo htmlspecialchars($currentSettings['daily_capping']); ?>" 
                                       step="0.01" min="0" required>
                                <div class="form-text">Maximum daily income limit per user</div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="min_withdrawal" class="form-label">Minimum Withdrawal (₹)</label>
                                <input type="number" class="form-control" id="min_withdrawal" name="min_withdrawal" 
                                       value="<?php echo htmlspecialchars($currentSettings['min_withdrawal']); ?>" 
                                       step="0.01" min="0" required>
                                <div class="form-text">Minimum amount for withdrawal requests</div>
                            </div>
                            
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>Update MLM Settings
                            </button>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Company Information -->
            <div class="col-md-6">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-building me-2"></i>Company Information</h5>
                    </div>
                    <div class="card-body">
                        <form method="POST">
                            <?php echo generateCsrfToken(); ?>
                            <input type="hidden" name="action" value="update_company_info">
                            
                            <div class="mb-3">
                                <label for="company_name" class="form-label">Company Name</label>
                                <input type="text" class="form-control" id="company_name" name="company_name" 
                                       value="<?php echo htmlspecialchars($currentSettings['company_name']); ?>" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="support_email" class="form-label">Support Email</label>
                                <input type="email" class="form-control" id="support_email" name="support_email" 
                                       value="<?php echo htmlspecialchars($currentSettings['support_email']); ?>" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="support_phone" class="form-label">Support Phone</label>
                                <input type="text" class="form-control" id="support_phone" name="support_phone" 
                                       value="<?php echo htmlspecialchars($currentSettings['support_phone']); ?>" required>
                            </div>
                            
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>Update Company Info
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Payment Settings -->
            <div class="col-md-6">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-credit-card me-2"></i>Payment Settings</h5>
                    </div>
                    <div class="card-body">
                        <form method="POST">
                            <?php echo generateCsrfToken(); ?>
                            <input type="hidden" name="action" value="update_payment_settings">
                            
                            <div class="mb-3">
                                <label for="razorpay_mode" class="form-label">Razorpay Mode</label>
                                <select class="form-select" id="razorpay_mode" name="razorpay_mode" required>
                                    <option value="test" <?php echo ($currentSettings['razorpay_mode'] === 'test') ? 'selected' : ''; ?>>Test Mode</option>
                                    <option value="live" <?php echo ($currentSettings['razorpay_mode'] === 'live') ? 'selected' : ''; ?>>Live Mode</option>
                                </select>
                            </div>
                            
                            <div class="mb-3">
                                <label for="razorpay_key_id" class="form-label">Razorpay Key ID</label>
                                <input type="text" class="form-control" id="razorpay_key_id" name="razorpay_key_id" 
                                       value="<?php echo htmlspecialchars($currentSettings['razorpay_key_id']); ?>">
                                <div class="form-text">Your Razorpay API Key ID</div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="razorpay_key_secret" class="form-label">Razorpay Key Secret</label>
                                <input type="password" class="form-control" id="razorpay_key_secret" name="razorpay_key_secret" 
                                       value="<?php echo htmlspecialchars($currentSettings['razorpay_key_secret']); ?>">
                                <div class="form-text">Your Razorpay API Key Secret</div>
                            </div>
                            
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>Update Payment Settings
                            </button>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Change Password -->
            <div class="col-md-6">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-lock me-2"></i>Change Password</h5>
                    </div>
                    <div class="card-body">
                        <form method="POST">
                            <?php echo generateCsrfToken(); ?>
                            <input type="hidden" name="action" value="change_password">
                            
                            <div class="mb-3">
                                <label for="current_password" class="form-label">Current Password</label>
                                <input type="password" class="form-control" id="current_password" name="current_password" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="new_password" class="form-label">New Password</label>
                                <input type="password" class="form-control" id="new_password" name="new_password" 
                                       minlength="6" required>
                                <div class="form-text">Minimum 6 characters</div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="confirm_password" class="form-label">Confirm New Password</label>
                                <input type="password" class="form-control" id="confirm_password" name="confirm_password" 
                                       minlength="6" required>
                            </div>
                            
                            <button type="submit" class="btn btn-warning">
                                <i class="fas fa-key me-2"></i>Change Password
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- System Information -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>System Information</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <strong>PHP Version:</strong><br>
                                <span class="text-muted"><?php echo PHP_VERSION; ?></span>
                            </div>
                            <div class="col-md-3">
                                <strong>Database:</strong><br>
                                <span class="text-muted">MySQL</span>
                            </div>
                            <div class="col-md-3">
                                <strong>System:</strong><br>
                                <span class="text-muted"><?php echo php_uname('s'); ?></span>
                            </div>
                            <div class="col-md-3">
                                <strong>Last Updated:</strong><br>
                                <span class="text-muted"><?php echo date('Y-m-d H:i:s'); ?></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
