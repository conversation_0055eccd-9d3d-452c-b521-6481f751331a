<?php
/**
 * Logger Class
 * MLM Binary Plan System
 * 
 * This class provides centralized logging functionality for the application
 */

class Logger {
    private static $instance = null;
    private $logDirectory;
    private $maxFileSize = 10485760; // 10MB
    private $maxFiles = 5;
    
    // Log levels
    const DEBUG = 'DEBUG';
    const INFO = 'INFO';
    const WARNING = 'WARNING';
    const ERROR = 'ERROR';
    const CRITICAL = 'CRITICAL';
    
    /**
     * Constructor
     */
    private function __construct() {
        $this->logDirectory = dirname(__DIR__) . '/logs/';
        
        // Create logs directory if it doesn't exist
        if (!is_dir($this->logDirectory)) {
            mkdir($this->logDirectory, 0755, true);
        }
    }
    
    /**
     * Get singleton instance
     * 
     * @return Logger
     */
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Log a message
     * 
     * @param string $level Log level
     * @param string $message Log message
     * @param array $context Additional context data
     * @param string $category Log category (default: 'general')
     */
    public function log($level, $message, array $context = [], $category = 'general') {
        $timestamp = date('Y-m-d H:i:s');
        $logEntry = $this->formatLogEntry($timestamp, $level, $message, $context);
        
        // Write to file
        $this->writeToFile($category, $logEntry);
        
        // Also write to database if it's an error or critical
        if (in_array($level, [self::ERROR, self::CRITICAL])) {
            $this->writeToDatabase($level, $message, $context);
        }
    }
    
    /**
     * Log debug message
     * 
     * @param string $message Log message
     * @param array $context Additional context data
     * @param string $category Log category
     */
    public function debug($message, array $context = [], $category = 'general') {
        $this->log(self::DEBUG, $message, $context, $category);
    }
    
    /**
     * Log info message
     * 
     * @param string $message Log message
     * @param array $context Additional context data
     * @param string $category Log category
     */
    public function info($message, array $context = [], $category = 'general') {
        $this->log(self::INFO, $message, $context, $category);
    }
    
    /**
     * Log warning message
     * 
     * @param string $message Log message
     * @param array $context Additional context data
     * @param string $category Log category
     */
    public function warning($message, array $context = [], $category = 'general') {
        $this->log(self::WARNING, $message, $context, $category);
    }
    
    /**
     * Log error message
     * 
     * @param string $message Log message
     * @param array $context Additional context data
     * @param string $category Log category
     */
    public function error($message, array $context = [], $category = 'general') {
        $this->log(self::ERROR, $message, $context, $category);
    }
    
    /**
     * Log critical message
     * 
     * @param string $message Log message
     * @param array $context Additional context data
     * @param string $category Log category
     */
    public function critical($message, array $context = [], $category = 'general') {
        $this->log(self::CRITICAL, $message, $context, $category);
    }
    
    /**
     * Log authentication events
     * 
     * @param string $userType User type (admin, franchise, user)
     * @param string $userId User ID
     * @param string $action Action (login, logout, failed_login)
     * @param array $context Additional context data
     */
    public function logAuth($userType, $userId, $action, array $context = []) {
        $message = "Authentication: {$userType} {$userId} - {$action}";
        $context['user_type'] = $userType;
        $context['user_id'] = $userId;
        $context['action'] = $action;
        $context['ip_address'] = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
        $context['user_agent'] = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';
        
        $this->info($message, $context, 'auth');
    }
    
    /**
     * Log PV transactions
     * 
     * @param string $userId User ID
     * @param float $pvAmount PV amount
     * @param string $side Side (left, right, self, upline)
     * @param string $transactionType Transaction type
     * @param array $context Additional context data
     */
    public function logPVTransaction($userId, $pvAmount, $side, $transactionType, array $context = []) {
        $message = "PV Transaction: {$userId} - {$pvAmount} PV ({$side} side, {$transactionType})";
        $context['user_id'] = $userId;
        $context['pv_amount'] = $pvAmount;
        $context['side'] = $side;
        $context['transaction_type'] = $transactionType;
        
        $this->info($message, $context, 'pv');
    }
    
    /**
     * Log payout events
     * 
     * @param string $userId User ID
     * @param float $amount Payout amount
     * @param string $type Payout type (matching, bonus, etc.)
     * @param array $context Additional context data
     */
    public function logPayout($userId, $amount, $type, array $context = []) {
        $message = "Payout: {$userId} - ₹{$amount} ({$type})";
        $context['user_id'] = $userId;
        $context['amount'] = $amount;
        $context['payout_type'] = $type;
        
        $this->info($message, $context, 'payout');
    }
    
    /**
     * Log cron job events
     * 
     * @param string $jobName Cron job name
     * @param string $status Status (started, completed, failed)
     * @param array $context Additional context data
     */
    public function logCron($jobName, $status, array $context = []) {
        $message = "Cron Job: {$jobName} - {$status}";
        $context['job_name'] = $jobName;
        $context['status'] = $status;
        
        if ($status === 'failed') {
            $this->error($message, $context, 'cron');
        } else {
            $this->info($message, $context, 'cron');
        }
    }
    
    /**
     * Log system events
     * 
     * @param string $event Event name
     * @param string $message Event message
     * @param array $context Additional context data
     */
    public function logSystem($event, $message, array $context = []) {
        $logMessage = "System Event: {$event} - {$message}";
        $context['event'] = $event;
        
        $this->info($logMessage, $context, 'system');
    }
    
    /**
     * Format log entry
     * 
     * @param string $timestamp Timestamp
     * @param string $level Log level
     * @param string $message Log message
     * @param array $context Additional context data
     * @return string Formatted log entry
     */
    private function formatLogEntry($timestamp, $level, $message, array $context = []) {
        $contextStr = '';
        if (!empty($context)) {
            $contextStr = ' | Context: ' . json_encode($context);
        }
        
        return "[{$timestamp}] {$level}: {$message}{$contextStr}" . PHP_EOL;
    }
    
    /**
     * Write log entry to file
     * 
     * @param string $category Log category
     * @param string $logEntry Log entry
     */
    private function writeToFile($category, $logEntry) {
        $filename = $this->logDirectory . $category . '_' . date('Y-m-d') . '.log';
        
        // Check if file needs rotation
        if (file_exists($filename) && filesize($filename) > $this->maxFileSize) {
            $this->rotateLogFile($filename);
        }
        
        // Write to file
        file_put_contents($filename, $logEntry, FILE_APPEND | LOCK_EX);
    }
    
    /**
     * Write log entry to database
     * 
     * @param string $level Log level
     * @param string $message Log message
     * @param array $context Additional context data
     */
    private function writeToDatabase($level, $message, array $context = []) {
        try {
            $db = Database::getInstance();
            $stmt = $db->prepare("INSERT INTO system_logs (log_type, message, created_at) VALUES (?, ?, NOW())");
            
            $logType = strtolower($level);
            $fullMessage = $message;
            if (!empty($context)) {
                $fullMessage .= ' | Context: ' . json_encode($context);
            }
            
            $stmt->execute([$logType, $fullMessage]);
        } catch (Exception $e) {
            // If database logging fails, write to error log
            error_log("Logger: Failed to write to database - " . $e->getMessage());
        }
    }
    
    /**
     * Rotate log file
     * 
     * @param string $filename Log file name
     */
    private function rotateLogFile($filename) {
        $baseFilename = pathinfo($filename, PATHINFO_FILENAME);
        $extension = pathinfo($filename, PATHINFO_EXTENSION);
        $directory = pathinfo($filename, PATHINFO_DIRNAME);
        
        // Rotate existing files
        for ($i = $this->maxFiles - 1; $i >= 1; $i--) {
            $oldFile = $directory . '/' . $baseFilename . '.' . $i . '.' . $extension;
            $newFile = $directory . '/' . $baseFilename . '.' . ($i + 1) . '.' . $extension;
            
            if (file_exists($oldFile)) {
                if ($i + 1 > $this->maxFiles) {
                    unlink($oldFile); // Delete oldest file
                } else {
                    rename($oldFile, $newFile);
                }
            }
        }
        
        // Move current file to .1
        $rotatedFile = $directory . '/' . $baseFilename . '.1.' . $extension;
        rename($filename, $rotatedFile);
    }
    
    /**
     * Clean old log files
     * 
     * @param int $days Number of days to keep logs
     */
    public function cleanOldLogs($days = 30) {
        $cutoffTime = time() - ($days * 24 * 60 * 60);
        
        $files = glob($this->logDirectory . '*.log*');
        foreach ($files as $file) {
            if (filemtime($file) < $cutoffTime) {
                unlink($file);
            }
        }
    }
    
    /**
     * Get log files
     * 
     * @param string $category Log category (optional)
     * @return array Log files
     */
    public function getLogFiles($category = null) {
        if ($category) {
            $pattern = $this->logDirectory . $category . '_*.log*';
        } else {
            $pattern = $this->logDirectory . '*.log*';
        }
        
        $files = glob($pattern);
        
        // Sort by modification time (newest first)
        usort($files, function($a, $b) {
            return filemtime($b) - filemtime($a);
        });
        
        return $files;
    }
    
    /**
     * Read log file
     * 
     * @param string $filename Log file name
     * @param int $lines Number of lines to read from the end
     * @return array Log lines
     */
    public function readLogFile($filename, $lines = 100) {
        if (!file_exists($filename)) {
            return [];
        }
        
        $file = new SplFileObject($filename);
        $file->seek(PHP_INT_MAX);
        $totalLines = $file->key();
        
        $startLine = max(0, $totalLines - $lines);
        $logLines = [];
        
        $file->seek($startLine);
        while (!$file->eof()) {
            $line = trim($file->current());
            if (!empty($line)) {
                $logLines[] = $line;
            }
            $file->next();
        }
        
        return $logLines;
    }
}
?>
