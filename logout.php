<?php
/**
 * Logout Script
 * MLM Binary Plan System
 * Works for all user types (admin, franchise, user)
 */

require_once 'includes/header.php';

// Check if user is logged in
if (!isLoggedIn()) {
    Response::redirect('index.php');
}

try {
    $userType = getCurrentUserType();
    $userId = getCurrentUserId();
    
    // Log logout activity
    if ($userType && $userId) {
        $db = Database::getInstance();
        $stmt = $db->prepare("UPDATE login_logs SET logout_time = NOW() WHERE user_type = ? AND user_id = ? AND logout_time IS NULL ORDER BY login_time DESC LIMIT 1");
        $stmt->execute([$userType, $userId]);
    }
    
    // Destroy session
    destroyUserSession();
    
    // Set logout message
    session_start();
    setSuccessMessage('You have been logged out successfully.');
    
    // Redirect based on user type
    switch ($userType) {
        case 'admin':
            Response::redirect('admin/login.php');
            break;
        case 'franchise':
            Response::redirect('franchise/login.php');
            break;
        case 'user':
            Response::redirect('user/login.php');
            break;
        default:
            Response::redirect('index.php');
            break;
    }
    
} catch (Exception $e) {
    error_log("Logout error: " . $e->getMessage());
    
    // Force session destruction and redirect
    session_unset();
    session_destroy();
    Response::redirect('index.php');
}
?>
