<?php
/**
 * Admin Withdrawals Management
 * MLM Binary Plan System
 */

require_once '../includes/header.php';
require_once '../includes/Auth.php';
require_once '../includes/Wallet.php';

// Require admin authentication
Auth::requireAdmin();

$currentUser = Auth::user();
$adminId = Auth::id();

$message = '';
$error = '';

// Handle withdrawal processing
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'process_withdrawal') {
    verifyCsrfToken();
    
    $withdrawalId = (int) $_POST['withdrawal_id'];
    $status = sanitizeInput($_POST['status']);
    $adminNotes = sanitizeInput($_POST['admin_notes']);
    
    if (in_array($status, ['approved', 'rejected'])) {
        try {
            $wallet = new Wallet();
            $wallet->processWithdrawal($withdrawalId, $status, $adminId, $adminNotes);
            
            setSuccessMessage("Withdrawal {$status} successfully");
            Response::redirect('withdrawals.php');
        } catch (Exception $e) {
            $error = $e->getMessage();
        }
    }
}

// Get filter parameters
$status = $_GET['status'] ?? 'pending';
$page = (int) ($_GET['page'] ?? 1);
$limit = 20;
$offset = ($page - 1) * $limit;

// Get withdrawals
$db = Database::getInstance();
$whereClause = $status ? "WHERE w.status = ?" : "";
$params = $status ? [$status] : [];

$withdrawalsStmt = $db->prepare("
    SELECT w.*, u.full_name, u.email, u.phone, a.full_name as processed_by_name
    FROM withdrawals w 
    JOIN users u ON w.user_id = u.user_id 
    LEFT JOIN admin a ON w.processed_by = a.id
    {$whereClause}
    ORDER BY w.requested_at DESC 
    LIMIT ? OFFSET ?
");
$params[] = $limit;
$params[] = $offset;
$withdrawalsStmt->execute($params);
$withdrawals = $withdrawalsStmt->fetchAll();

// Get total count for pagination
$countParams = $status ? [$status] : [];
$countStmt = $db->prepare("SELECT COUNT(*) FROM withdrawals w {$whereClause}");
$countStmt->execute($countParams);
$totalWithdrawals = $countStmt->fetchColumn();
$totalPages = ceil($totalWithdrawals / $limit);

// Get statistics
$stats = $db->query("
    SELECT 
        COUNT(*) as total,
        SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending,
        SUM(CASE WHEN status = 'approved' THEN 1 ELSE 0 END) as approved,
        SUM(CASE WHEN status = 'rejected' THEN 1 ELSE 0 END) as rejected,
        SUM(CASE WHEN status = 'pending' THEN amount ELSE 0 END) as pending_amount,
        SUM(CASE WHEN status = 'approved' THEN amount ELSE 0 END) as approved_amount
    FROM withdrawals
")->fetch();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Withdrawals Management - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
</head>
<body>
    <?php include 'includes/navigation.php'; ?>

    <div class="container-fluid mt-4">
        <!-- Flash Messages -->
        <?php displayFlashMessages(); ?>
        
        <?php if ($error): ?>
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle me-2"></i><?php echo htmlspecialchars($error); ?>
            </div>
        <?php endif; ?>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="stats-card">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon warning me-3">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div>
                            <h4 class="mb-0"><?php echo $stats['pending']; ?></h4>
                            <small class="text-muted">Pending</small>
                            <div class="mt-1">
                                <small class="text-warning"><?php echo formatCurrency($stats['pending_amount']); ?></small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3">
                <div class="stats-card">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon success me-3">
                            <i class="fas fa-check"></i>
                        </div>
                        <div>
                            <h4 class="mb-0"><?php echo $stats['approved']; ?></h4>
                            <small class="text-muted">Approved</small>
                            <div class="mt-1">
                                <small class="text-success"><?php echo formatCurrency($stats['approved_amount']); ?></small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3">
                <div class="stats-card">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon danger me-3">
                            <i class="fas fa-times"></i>
                        </div>
                        <div>
                            <h4 class="mb-0"><?php echo $stats['rejected']; ?></h4>
                            <small class="text-muted">Rejected</small>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3">
                <div class="stats-card">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon primary me-3">
                            <i class="fas fa-list"></i>
                        </div>
                        <div>
                            <h4 class="mb-0"><?php echo $stats['total']; ?></h4>
                            <small class="text-muted">Total</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filter Tabs -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <ul class="nav nav-tabs card-header-tabs">
                            <li class="nav-item">
                                <a class="nav-link <?php echo $status === 'pending' ? 'active' : ''; ?>" href="?status=pending">
                                    <i class="fas fa-clock me-1"></i>Pending (<?php echo $stats['pending']; ?>)
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link <?php echo $status === 'approved' ? 'active' : ''; ?>" href="?status=approved">
                                    <i class="fas fa-check me-1"></i>Approved (<?php echo $stats['approved']; ?>)
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link <?php echo $status === 'rejected' ? 'active' : ''; ?>" href="?status=rejected">
                                    <i class="fas fa-times me-1"></i>Rejected (<?php echo $stats['rejected']; ?>)
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link <?php echo $status === '' ? 'active' : ''; ?>" href="?status=">
                                    <i class="fas fa-list me-1"></i>All (<?php echo $stats['total']; ?>)
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- Withdrawals Table -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <?php if (!empty($withdrawals)): ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>User</th>
                                            <th>Amount</th>
                                            <th>Bank Details</th>
                                            <th>Status</th>
                                            <th>Requested</th>
                                            <th>Processed By</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($withdrawals as $withdrawal): ?>
                                            <tr>
                                                <td><strong>#<?php echo $withdrawal['id']; ?></strong></td>
                                                <td>
                                                    <div>
                                                        <strong><?php echo htmlspecialchars($withdrawal['full_name']); ?></strong>
                                                        <br>
                                                        <small class="text-muted"><?php echo htmlspecialchars($withdrawal['user_id']); ?></small>
                                                        <br>
                                                        <small class="text-muted"><?php echo htmlspecialchars($withdrawal['email']); ?></small>
                                                    </div>
                                                </td>
                                                <td>
                                                    <strong class="text-success"><?php echo formatCurrency($withdrawal['amount']); ?></strong>
                                                </td>
                                                <td>
                                                    <?php 
                                                    $bankDetails = json_decode($withdrawal['bank_details'], true);
                                                    if ($bankDetails):
                                                    ?>
                                                        <small>
                                                            <strong><?php echo htmlspecialchars($bankDetails['bank_name'] ?? ''); ?></strong><br>
                                                            A/C: <?php echo htmlspecialchars($bankDetails['account_number'] ?? ''); ?><br>
                                                            IFSC: <?php echo htmlspecialchars($bankDetails['ifsc_code'] ?? ''); ?><br>
                                                            Holder: <?php echo htmlspecialchars($bankDetails['account_holder'] ?? ''); ?>
                                                        </small>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <?php
                                                    $statusClass = '';
                                                    switch ($withdrawal['status']) {
                                                        case 'pending': $statusClass = 'warning'; break;
                                                        case 'approved': $statusClass = 'success'; break;
                                                        case 'rejected': $statusClass = 'danger'; break;
                                                    }
                                                    ?>
                                                    <span class="badge bg-<?php echo $statusClass; ?>">
                                                        <?php echo ucfirst($withdrawal['status']); ?>
                                                    </span>
                                                </td>
                                                <td><?php echo date('M d, Y H:i', strtotime($withdrawal['requested_at'])); ?></td>
                                                <td>
                                                    <?php if ($withdrawal['processed_by_name']): ?>
                                                        <small><?php echo htmlspecialchars($withdrawal['processed_by_name']); ?></small>
                                                        <br>
                                                        <small class="text-muted"><?php echo date('M d, Y', strtotime($withdrawal['processed_at'])); ?></small>
                                                    <?php else: ?>
                                                        <span class="text-muted">-</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <?php if ($withdrawal['status'] === 'pending'): ?>
                                                        <div class="btn-group btn-group-sm">
                                                            <button class="btn btn-success" onclick="processWithdrawal(<?php echo $withdrawal['id']; ?>, 'approved')">
                                                                <i class="fas fa-check"></i>
                                                            </button>
                                                            <button class="btn btn-danger" onclick="processWithdrawal(<?php echo $withdrawal['id']; ?>, 'rejected')">
                                                                <i class="fas fa-times"></i>
                                                            </button>
                                                        </div>
                                                    <?php else: ?>
                                                        <span class="text-muted">Processed</span>
                                                    <?php endif; ?>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                            
                            <!-- Pagination -->
                            <?php if ($totalPages > 1): ?>
                                <nav aria-label="Withdrawals pagination">
                                    <ul class="pagination justify-content-center">
                                        <?php if ($page > 1): ?>
                                            <li class="page-item">
                                                <a class="page-link" href="?page=<?php echo $page - 1; ?>&status=<?php echo urlencode($status); ?>">Previous</a>
                                            </li>
                                        <?php endif; ?>
                                        
                                        <?php for ($i = max(1, $page - 2); $i <= min($totalPages, $page + 2); $i++): ?>
                                            <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                                                <a class="page-link" href="?page=<?php echo $i; ?>&status=<?php echo urlencode($status); ?>"><?php echo $i; ?></a>
                                            </li>
                                        <?php endfor; ?>
                                        
                                        <?php if ($page < $totalPages): ?>
                                            <li class="page-item">
                                                <a class="page-link" href="?page=<?php echo $page + 1; ?>&status=<?php echo urlencode($status); ?>">Next</a>
                                            </li>
                                        <?php endif; ?>
                                    </ul>
                                </nav>
                            <?php endif; ?>
                        <?php else: ?>
                            <div class="text-center py-5">
                                <i class="fas fa-money-bill-wave fa-4x text-muted mb-3"></i>
                                <h5>No withdrawals found</h5>
                                <p class="text-muted">No withdrawal requests match your filter</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Process Withdrawal Modal -->
    <div class="modal fade" id="processModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="processModalTitle">Process Withdrawal</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST">
                    <?php echo csrfTokenInput(); ?>
                    <input type="hidden" name="action" value="process_withdrawal">
                    <input type="hidden" name="withdrawal_id" id="withdrawalId">
                    <input type="hidden" name="status" id="withdrawalStatus">
                    
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="admin_notes" class="form-label">Admin Notes</label>
                            <textarea class="form-control" name="admin_notes" rows="3" placeholder="Optional notes about this decision"></textarea>
                        </div>
                        
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <span id="processMessage"></span>
                        </div>
                    </div>
                    
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn" id="processBtn">Process</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function processWithdrawal(withdrawalId, status) {
            document.getElementById('withdrawalId').value = withdrawalId;
            document.getElementById('withdrawalStatus').value = status;
            
            const modal = document.getElementById('processModal');
            const title = document.getElementById('processModalTitle');
            const message = document.getElementById('processMessage');
            const btn = document.getElementById('processBtn');
            
            if (status === 'approved') {
                title.textContent = 'Approve Withdrawal';
                message.textContent = 'This will approve the withdrawal and debit the amount from user\'s wallet.';
                btn.textContent = 'Approve';
                btn.className = 'btn btn-success';
            } else {
                title.textContent = 'Reject Withdrawal';
                message.textContent = 'This will reject the withdrawal request. The amount will remain in user\'s wallet.';
                btn.textContent = 'Reject';
                btn.className = 'btn btn-danger';
            }
            
            const bootstrapModal = new bootstrap.Modal(modal);
            bootstrapModal.show();
        }
    </script>
</body>
</html>
