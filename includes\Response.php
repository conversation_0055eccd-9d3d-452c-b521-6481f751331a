<?php
/**
 * Response Helper Class
 * MLM Binary Plan System
 */

class Response {
    
    /**
     * Send JSON response
     */
    public static function json($data, $statusCode = 200) {
        http_response_code($statusCode);
        header('Content-Type: application/json');
        echo json_encode($data);
        exit();
    }
    
    /**
     * Send success response
     */
    public static function success($message = 'Success', $data = null, $statusCode = 200) {
        $response = [
            'success' => true,
            'message' => $message
        ];
        
        if ($data !== null) {
            $response['data'] = $data;
        }
        
        self::json($response, $statusCode);
    }
    
    /**
     * Send error response
     */
    public static function error($message = 'Error', $errors = null, $statusCode = 400) {
        $response = [
            'success' => false,
            'message' => $message
        ];
        
        if ($errors !== null) {
            $response['errors'] = $errors;
        }
        
        self::json($response, $statusCode);
    }
    
    /**
     * Send validation error response
     */
    public static function validationError($errors, $message = 'Validation failed') {
        self::error($message, $errors, 422);
    }
    
    /**
     * Send unauthorized response
     */
    public static function unauthorized($message = 'Unauthorized') {
        self::error($message, null, 401);
    }
    
    /**
     * Send forbidden response
     */
    public static function forbidden($message = 'Forbidden') {
        self::error($message, null, 403);
    }
    
    /**
     * Send not found response
     */
    public static function notFound($message = 'Not found') {
        self::error($message, null, 404);
    }
    
    /**
     * Send server error response
     */
    public static function serverError($message = 'Internal server error') {
        self::error($message, null, 500);
    }
    
    /**
     * Redirect to URL
     */
    public static function redirect($url, $statusCode = 302) {
        http_response_code($statusCode);
        header("Location: {$url}");
        exit();
    }
    
    /**
     * Redirect back with message
     */
    public static function redirectBack($message = null, $type = 'info') {
        if ($message) {
            setFlashMessage($type, $message);
        }
        
        $referer = $_SERVER['HTTP_REFERER'] ?? 'index.php';
        self::redirect($referer);
    }
    
    /**
     * Redirect with success message
     */
    public static function redirectWithSuccess($url, $message) {
        setSuccessMessage($message);
        self::redirect($url);
    }
    
    /**
     * Redirect with error message
     */
    public static function redirectWithError($url, $message) {
        setErrorMessage($message);
        self::redirect($url);
    }
}
?>
