<?php
/**
 * Franchise Reports
 * MLM Binary Plan System
 */

require_once '../includes/header.php';
require_once '../includes/Auth.php';

// Require franchise authentication
if (!isLoggedIn('franchise')) {
    Response::redirect('login.php');
}

$currentUser = getCurrentUser();
$franchiseId = getCurrentUserId();

// Get franchise details
$db = Database::getInstance();
$franchiseStmt = $db->prepare("SELECT * FROM franchise WHERE id = ?");
$franchiseStmt->execute([$franchiseId]);
$franchiseDetails = $franchiseStmt->fetch();

// Get date range
$startDate = $_GET['start_date'] ?? date('Y-m-01'); // First day of current month
$endDate = $_GET['end_date'] ?? date('Y-m-d'); // Today

// Get user registration report
$userReportStmt = $db->prepare("
    SELECT 
        DATE(registration_date) as date,
        COUNT(*) as registrations
    FROM users 
    WHERE franchise_id = ? 
    AND DATE(registration_date) BETWEEN ? AND ?
    GROUP BY DATE(registration_date)
    ORDER BY date DESC
");
$userReportStmt->execute([$franchiseId, $startDate, $endDate]);
$userReport = $userReportStmt->fetchAll();

// Get PV report
$pvReportStmt = $db->prepare("
    SELECT 
        DATE(pt.created_at) as date,
        COUNT(*) as transactions,
        SUM(pt.pv_amount) as total_pv,
        SUM(CASE WHEN pt.side = 'left' THEN pt.pv_amount ELSE 0 END) as left_pv,
        SUM(CASE WHEN pt.side = 'right' THEN pt.pv_amount ELSE 0 END) as right_pv
    FROM pv_transactions pt
    JOIN users u ON pt.user_id = u.user_id
    WHERE u.franchise_id = ? 
    AND pt.created_by_type = 'franchise'
    AND DATE(pt.created_at) BETWEEN ? AND ?
    GROUP BY DATE(pt.created_at)
    ORDER BY date DESC
");
$pvReportStmt->execute([$franchiseId, $startDate, $endDate]);
$pvReport = $pvReportStmt->fetchAll();

// Get summary statistics
$summaryStmt = $db->prepare("
    SELECT 
        COUNT(DISTINCT u.user_id) as total_users,
        COUNT(DISTINCT pt.id) as total_transactions,
        SUM(pt.pv_amount) as total_pv,
        SUM(pt.pv_amount * ?) as estimated_commission
    FROM users u
    LEFT JOIN pv_transactions pt ON u.user_id = pt.user_id AND pt.created_by_type = 'franchise'
    WHERE u.franchise_id = ?
    AND (pt.created_at IS NULL OR DATE(pt.created_at) BETWEEN ? AND ?)
");
$summaryStmt->execute([$franchiseDetails['commission_rate'] / 100, $franchiseId, $startDate, $endDate]);
$summary = $summaryStmt->fetch();

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reports - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
    <style>
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        }
        .stat-card {
            border-radius: 15px;
            border: none;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 sidebar p-0">
                <div class="p-3">
                    <div class="text-center text-white mb-4">
                        <i class="fas fa-store fa-3x mb-2"></i>
                        <h5><?php echo htmlspecialchars($franchiseDetails['full_name']); ?></h5>
                        <small><?php echo htmlspecialchars($franchiseDetails['franchise_code']); ?></small>
                    </div>
                    
                    <nav class="nav flex-column">
                        <a class="nav-link text-white" href="dashboard.php">
                            <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                        </a>
                        <a class="nav-link text-white" href="users.php">
                            <i class="fas fa-users me-2"></i>Manage Users
                        </a>
                        <a class="nav-link text-white" href="products.php">
                            <i class="fas fa-box me-2"></i>Product Assignment
                        </a>
                        <a class="nav-link text-white" href="pv-transactions.php">
                            <i class="fas fa-chart-line me-2"></i>PV Transactions
                        </a>
                        <a class="nav-link text-white" href="billing.php">
                            <i class="fas fa-file-invoice-dollar me-2"></i>Billing & Invoices
                        </a>
                        <a class="nav-link text-white active" href="reports.php">
                            <i class="fas fa-chart-bar me-2"></i>Reports
                        </a>
                        <a class="nav-link text-white" href="profile.php">
                            <i class="fas fa-user me-2"></i>Profile
                        </a>
                        <hr class="text-white">
                        <a class="nav-link text-white" href="../logout.php">
                            <i class="fas fa-sign-out-alt me-2"></i>Logout
                        </a>
                    </nav>
                </div>
            </div>
            
            <!-- Main Content -->
            <div class="col-md-9 col-lg-10">
                <div class="p-4">
                    <!-- Header -->
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h2>Reports & Analytics</h2>
                    </div>
                    
                    <!-- Date Range Filter -->
                    <div class="card mb-4">
                        <div class="card-body">
                            <form method="GET" action="">
                                <div class="row">
                                    <div class="col-md-3 mb-3">
                                        <label for="start_date" class="form-label">Start Date</label>
                                        <input type="date" class="form-control" id="start_date" name="start_date" value="<?php echo $startDate; ?>">
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <label for="end_date" class="form-label">End Date</label>
                                        <input type="date" class="form-control" id="end_date" name="end_date" value="<?php echo $endDate; ?>">
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <label class="form-label">&nbsp;</label>
                                        <div>
                                            <button type="submit" class="btn btn-primary">
                                                <i class="fas fa-filter me-2"></i>Apply Filter
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                    
                    <!-- Summary Statistics -->
                    <div class="row mb-4">
                        <div class="col-md-3 mb-3">
                            <div class="card stat-card">
                                <div class="card-body text-center">
                                    <i class="fas fa-users fa-2x text-primary mb-2"></i>
                                    <h3><?php echo $summary['total_users']; ?></h3>
                                    <small class="text-muted">Total Users</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="card stat-card">
                                <div class="card-body text-center">
                                    <i class="fas fa-chart-line fa-2x text-success mb-2"></i>
                                    <h3><?php echo $summary['total_transactions']; ?></h3>
                                    <small class="text-muted">PV Transactions</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="card stat-card">
                                <div class="card-body text-center">
                                    <i class="fas fa-coins fa-2x text-info mb-2"></i>
                                    <h3><?php echo formatPV($summary['total_pv'] ?? 0); ?></h3>
                                    <small class="text-muted">Total PV</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="card stat-card">
                                <div class="card-body text-center">
                                    <i class="fas fa-rupee-sign fa-2x text-warning mb-2"></i>
                                    <h3><?php echo formatCurrency($summary['estimated_commission'] ?? 0); ?></h3>
                                    <small class="text-muted">Est. Commission</small>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Reports -->
                    <div class="row">
                        <!-- User Registration Report -->
                        <div class="col-md-6 mb-4">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">Daily User Registrations</h5>
                                </div>
                                <div class="card-body">
                                    <?php if (!empty($userReport)): ?>
                                        <div class="table-responsive">
                                            <table class="table table-sm">
                                                <thead>
                                                    <tr>
                                                        <th>Date</th>
                                                        <th>Registrations</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <?php foreach ($userReport as $row): ?>
                                                        <tr>
                                                            <td><?php echo date('M d, Y', strtotime($row['date'])); ?></td>
                                                            <td><span class="badge bg-primary"><?php echo $row['registrations']; ?></span></td>
                                                        </tr>
                                                    <?php endforeach; ?>
                                                </tbody>
                                            </table>
                                        </div>
                                    <?php else: ?>
                                        <div class="text-center py-3">
                                            <i class="fas fa-chart-bar fa-2x text-muted mb-2"></i>
                                            <p class="text-muted">No registrations in selected period</p>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                        
                        <!-- PV Report -->
                        <div class="col-md-6 mb-4">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">Daily PV Transactions</h5>
                                </div>
                                <div class="card-body">
                                    <?php if (!empty($pvReport)): ?>
                                        <div class="table-responsive">
                                            <table class="table table-sm">
                                                <thead>
                                                    <tr>
                                                        <th>Date</th>
                                                        <th>Transactions</th>
                                                        <th>Total PV</th>
                                                        <th>L/R Split</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <?php foreach ($pvReport as $row): ?>
                                                        <tr>
                                                            <td><?php echo date('M d, Y', strtotime($row['date'])); ?></td>
                                                            <td><span class="badge bg-success"><?php echo $row['transactions']; ?></span></td>
                                                            <td><strong><?php echo formatPV($row['total_pv']); ?></strong></td>
                                                            <td>
                                                                <small class="text-info">L: <?php echo formatPV($row['left_pv']); ?></small><br>
                                                                <small class="text-warning">R: <?php echo formatPV($row['right_pv']); ?></small>
                                                            </td>
                                                        </tr>
                                                    <?php endforeach; ?>
                                                </tbody>
                                            </table>
                                        </div>
                                    <?php else: ?>
                                        <div class="text-center py-3">
                                            <i class="fas fa-chart-line fa-2x text-muted mb-2"></i>
                                            <p class="text-muted">No PV transactions in selected period</p>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
