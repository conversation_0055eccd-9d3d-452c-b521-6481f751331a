Stack trace:
Frame         Function      Args
0007FFFFABB0  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFF9AB0) msys-2.0.dll+0x1FE8E
0007FFFFABB0  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFAE88) msys-2.0.dll+0x67F9
0007FFFFABB0  000210046832 (000210286019, 0007FFFFAA68, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFFABB0  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFABB0  000210068E24 (0007FFFFABC0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFAE90  00021006A225 (0007FFFFABC0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFB86E50000 ntdll.dll
7FFB86400000 KERNEL32.DLL
7FFB84700000 KERNELBASE.dll
7FFB85420000 USER32.dll
7FFB846D0000 win32u.dll
7FFB855F0000 GDI32.dll
7FFB840B0000 gdi32full.dll
7FFB841E0000 msvcp_win.dll
000210040000 msys-2.0.dll
7FFB843F0000 ucrtbase.dll
7FFB864D0000 advapi32.dll
7FFB85780000 msvcrt.dll
7FFB86B30000 sechost.dll
7FFB84340000 bcrypt.dll
7FFB84AE0000 RPCRT4.dll
7FFB835D0000 CRYPTBASE.DLL
7FFB84370000 bcryptPrimitives.dll
7FFB85AB0000 IMM32.DLL
