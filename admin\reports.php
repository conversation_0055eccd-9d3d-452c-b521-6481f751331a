<?php
/**
 * System Reports - Admin Panel
 * MLM Binary Plan System
 */

require_once '../includes/header.php';
require_once '../includes/Auth.php';
require_once '../includes/PVSystem.php';
require_once '../includes/Wallet.php';

// Require admin authentication
Auth::requireAdmin();

$currentUser = Auth::user();
$adminId = Auth::id();

// Initialize classes
$pvSystem = new PVSystem();
$wallet = new Wallet();

// Get database instance
$db = Database::getInstance();

// Get date range for reports (default to last 30 days)
$endDate = $_GET['end_date'] ?? date('Y-m-d');
$startDate = $_GET['start_date'] ?? date('Y-m-d', strtotime('-30 days'));

// User Statistics
$userStats = $db->prepare("
    SELECT 
        COUNT(*) as total_users,
        SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active_users,
        SUM(CASE WHEN status = 'inactive' THEN 1 ELSE 0 END) as inactive_users,
        SUM(CASE WHEN DATE(registration_date) BETWEEN ? AND ? THEN 1 ELSE 0 END) as period_registrations
    FROM users
");
$userStats->execute([$startDate, $endDate]);
$userStatsData = $userStats->fetch();

// PV Statistics
$pvStats = $db->prepare("
    SELECT 
        COUNT(*) as total_transactions,
        SUM(pv_amount) as total_pv,
        AVG(pv_amount) as avg_pv,
        SUM(CASE WHEN transaction_type = 'product_purchase' THEN pv_amount ELSE 0 END) as purchase_pv,
        SUM(CASE WHEN transaction_type = 'franchise_assignment' THEN pv_amount ELSE 0 END) as franchise_pv
    FROM pv_transactions 
    WHERE DATE(transaction_date) BETWEEN ? AND ?
");
$pvStats->execute([$startDate, $endDate]);
$pvStatsData = $pvStats->fetch();

// Wallet Statistics
$walletStats = $db->prepare("
    SELECT 
        COUNT(DISTINCT user_id) as users_with_balance,
        SUM(balance) as total_balance,
        AVG(balance) as avg_balance,
        MAX(balance) as max_balance
    FROM wallet 
    WHERE balance > 0
");
$walletStats->execute();
$walletStatsData = $walletStats->fetch();

// Withdrawal Statistics
$withdrawalStats = $db->prepare("
    SELECT 
        COUNT(*) as total_requests,
        SUM(amount) as total_amount,
        SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending_requests,
        SUM(CASE WHEN status = 'approved' THEN 1 ELSE 0 END) as approved_requests,
        SUM(CASE WHEN status = 'rejected' THEN 1 ELSE 0 END) as rejected_requests,
        SUM(CASE WHEN status = 'approved' THEN amount ELSE 0 END) as approved_amount
    FROM withdrawals 
    WHERE DATE(requested_at) BETWEEN ? AND ?
");
$withdrawalStats->execute([$startDate, $endDate]);
$withdrawalStatsData = $withdrawalStats->fetch();

// Product Statistics
$productStats = $db->prepare("
    SELECT 
        COUNT(*) as total_products,
        SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active_products,
        AVG(price) as avg_price,
        AVG(pv_value) as avg_pv_value
    FROM products
");
$productStats->execute();
$productStatsData = $productStats->fetch();

// Top Performers (by PV)
$topPerformers = $db->prepare("
    SELECT u.user_id, u.full_name, u.email, SUM(pt.pv_amount) as total_pv
    FROM users u
    JOIN pv_transactions pt ON u.user_id = pt.user_id
    WHERE DATE(pt.transaction_date) BETWEEN ? AND ?
    GROUP BY u.user_id
    ORDER BY total_pv DESC
    LIMIT 10
");
$topPerformers->execute([$startDate, $endDate]);
$topPerformersData = $topPerformers->fetchAll();

// Recent Activities
$recentActivities = $db->prepare("
    SELECT 'User Registration' as activity_type, full_name as description, registration_date as activity_date
    FROM users 
    WHERE DATE(registration_date) BETWEEN ? AND ?
    UNION ALL
    SELECT 'PV Transaction' as activity_type, 
           CONCAT(u.full_name, ' - ', pt.pv_amount, ' PV') as description, 
           pt.transaction_date as activity_date
    FROM pv_transactions pt
    JOIN users u ON pt.user_id = u.user_id
    WHERE DATE(pt.transaction_date) BETWEEN ? AND ?
    UNION ALL
    SELECT 'Withdrawal Request' as activity_type,
           CONCAT(u.full_name, ' - ₹', w.amount) as description,
           w.requested_at as activity_date
    FROM withdrawals w
    JOIN users u ON w.user_id = u.user_id
    WHERE DATE(w.requested_at) BETWEEN ? AND ?
    ORDER BY activity_date DESC
    LIMIT 20
");
$recentActivities->execute([$startDate, $endDate, $startDate, $endDate, $startDate, $endDate]);
$recentActivitiesData = $recentActivities->fetchAll();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>System Reports - <?php echo SITE_NAME; ?> Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
</head>
<body>
    <?php include 'includes/navigation.php'; ?>

    <div class="container-fluid mt-4">
        <!-- Flash Messages -->
        <?php displayFlashMessages(); ?>
        
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <h2><i class="fas fa-chart-line me-2"></i>System Reports</h2>
                    
                    <!-- Date Range Filter -->
                    <form method="GET" class="d-flex gap-2">
                        <input type="date" name="start_date" value="<?php echo htmlspecialchars($startDate); ?>" class="form-control">
                        <input type="date" name="end_date" value="<?php echo htmlspecialchars($endDate); ?>" class="form-control">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-filter me-1"></i>Filter
                        </button>
                    </form>
                </div>
                <p class="text-muted">Period: <?php echo date('M d, Y', strtotime($startDate)); ?> to <?php echo date('M d, Y', strtotime($endDate)); ?></p>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card bg-primary text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title">Total Users</h6>
                                <h3><?php echo number_format($userStatsData['total_users']); ?></h3>
                                <small>Active: <?php echo number_format($userStatsData['active_users']); ?></small>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-users fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-success text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title">Total PV</h6>
                                <h3><?php echo number_format($pvStatsData['total_pv'] ?? 0); ?></h3>
                                <small>Transactions: <?php echo number_format($pvStatsData['total_transactions'] ?? 0); ?></small>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-chart-line fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-info text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title">Wallet Balance</h6>
                                <h3>₹<?php echo number_format($walletStatsData['total_balance'] ?? 0, 2); ?></h3>
                                <small>Users: <?php echo number_format($walletStatsData['users_with_balance'] ?? 0); ?></small>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-wallet fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-warning text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title">Withdrawals</h6>
                                <h3>₹<?php echo number_format($withdrawalStatsData['approved_amount'] ?? 0, 2); ?></h3>
                                <small>Pending: <?php echo number_format($withdrawalStatsData['pending_requests'] ?? 0); ?></small>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-money-bill-wave fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Detailed Reports -->
        <div class="row">
            <!-- Top Performers -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-trophy me-2"></i>Top Performers (by PV)</h5>
                    </div>
                    <div class="card-body">
                        <?php if (!empty($topPerformersData)): ?>
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>User</th>
                                            <th>Email</th>
                                            <th>Total PV</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($topPerformersData as $performer): ?>
                                            <tr>
                                                <td><?php echo htmlspecialchars($performer['full_name']); ?></td>
                                                <td><?php echo htmlspecialchars($performer['email']); ?></td>
                                                <td><strong><?php echo number_format($performer['total_pv']); ?></strong></td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php else: ?>
                            <p class="text-muted">No data available for the selected period.</p>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Recent Activities -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-clock me-2"></i>Recent Activities</h5>
                    </div>
                    <div class="card-body">
                        <?php if (!empty($recentActivitiesData)): ?>
                            <div class="list-group list-group-flush">
                                <?php foreach (array_slice($recentActivitiesData, 0, 10) as $activity): ?>
                                    <div class="list-group-item d-flex justify-content-between align-items-start">
                                        <div>
                                            <strong><?php echo htmlspecialchars($activity['activity_type']); ?></strong><br>
                                            <small><?php echo htmlspecialchars($activity['description']); ?></small>
                                        </div>
                                        <small class="text-muted">
                                            <?php echo date('M d, H:i', strtotime($activity['activity_date'])); ?>
                                        </small>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php else: ?>
                            <p class="text-muted">No activities found for the selected period.</p>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Export Options -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-download me-2"></i>Export Reports</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <a href="weekly-income-reports.php" class="btn btn-outline-primary w-100">
                                    <i class="fas fa-calendar-week me-2"></i>Weekly Income Reports
                                </a>
                            </div>
                            <div class="col-md-4">
                                <button class="btn btn-outline-secondary w-100" onclick="window.print()">
                                    <i class="fas fa-print me-2"></i>Print This Report
                                </button>
                            </div>
                            <div class="col-md-4">
                                <button class="btn btn-outline-info w-100" onclick="exportToCSV()">
                                    <i class="fas fa-file-csv me-2"></i>Export to CSV
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function exportToCSV() {
            alert('CSV export functionality will be implemented in a future update.');
        }
    </script>
</body>
</html>
