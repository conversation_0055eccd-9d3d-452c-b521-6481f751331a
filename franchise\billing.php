<?php
/**
 * Franchise Billing & Invoice Generation
 * MLM Binary Plan System
 */

require_once '../includes/header.php';
require_once '../includes/Auth.php';

// Require franchise authentication
if (!isLoggedIn('franchise')) {
    Response::redirect('login.php');
}

$currentUser = getCurrentUser();
$franchiseId = getCurrentUserId();
$error = '';
$success = '';

// Get franchise details
$db = Database::getInstance();
$franchiseStmt = $db->prepare("SELECT * FROM franchise WHERE id = ?");
$franchiseStmt->execute([$franchiseId]);
$franchiseDetails = $franchiseStmt->fetch();

// Handle invoice generation
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'generate_invoice') {
    verifyCsrfToken();
    
    $orderId = sanitizeInput($_POST['order_id']);
    
    // Get order details
    $orderStmt = $db->prepare("
        SELECT po.*, u.full_name, u.email, u.phone, u.address, p.name as product_name, p.description as product_description
        FROM purchase_orders po
        JOIN users u ON po.user_id = u.user_id
        JOIN products p ON po.product_id = p.id
        WHERE po.order_id = ? AND u.franchise_id = ?
    ");
    $orderStmt->execute([$orderId, $franchiseId]);
    $orderDetails = $orderStmt->fetch();
    
    if (!$orderDetails) {
        $error = "Order not found or doesn't belong to your franchise";
    } else {
        // Generate invoice (redirect to invoice view)
        Response::redirect("invoice.php?order_id=" . urlencode($orderId));
    }
}

// Get filter parameters
$status = $_GET['status'] ?? 'all';
$dateFrom = $_GET['date_from'] ?? '';
$dateTo = $_GET['date_to'] ?? '';
$search = $_GET['search'] ?? '';

// Build query conditions
$whereConditions = ["u.franchise_id = ?"];
$params = [$franchiseId];

if ($status && $status !== 'all') {
    $whereConditions[] = "po.order_status = ?";
    $params[] = $status;
}

if ($dateFrom) {
    $whereConditions[] = "DATE(po.created_at) >= ?";
    $params[] = $dateFrom;
}

if ($dateTo) {
    $whereConditions[] = "DATE(po.created_at) <= ?";
    $params[] = $dateTo;
}

if ($search) {
    $whereConditions[] = "(u.full_name LIKE ? OR u.user_id LIKE ? OR po.order_id LIKE ? OR p.name LIKE ?)";
    $searchTerm = "%{$search}%";
    $params[] = $searchTerm;
    $params[] = $searchTerm;
    $params[] = $searchTerm;
    $params[] = $searchTerm;
}

$whereClause = "WHERE " . implode(" AND ", $whereConditions);

// Pagination
$page = (int) ($_GET['page'] ?? 1);
$limit = 20;
$offset = ($page - 1) * $limit;

// Get orders
$ordersStmt = $db->prepare("
    SELECT po.*, u.full_name, u.user_id as user_code, u.email, p.name as product_name
    FROM purchase_orders po
    JOIN users u ON po.user_id = u.user_id
    JOIN products p ON po.product_id = p.id
    {$whereClause}
    ORDER BY po.created_at DESC
    LIMIT ? OFFSET ?
");
$params[] = $limit;
$params[] = $offset;
$ordersStmt->execute($params);
$orders = $ordersStmt->fetchAll();

// Get total count for pagination
$countParams = array_slice($params, 0, -2);
$countStmt = $db->prepare("
    SELECT COUNT(*) 
    FROM purchase_orders po
    JOIN users u ON po.user_id = u.user_id
    JOIN products p ON po.product_id = p.id
    {$whereClause}
");
$countStmt->execute($countParams);
$totalOrders = $countStmt->fetchColumn();
$totalPages = ceil($totalOrders / $limit);

// Get summary statistics
$summaryStmt = $db->prepare("
    SELECT 
        COUNT(*) as total_orders,
        SUM(po.total_amount) as total_revenue,
        SUM(po.pv_amount) as total_pv,
        COUNT(CASE WHEN po.order_status = 'confirmed' THEN 1 END) as confirmed_orders,
        COUNT(CASE WHEN po.order_status = 'pending' THEN 1 END) as pending_orders
    FROM purchase_orders po
    JOIN users u ON po.user_id = u.user_id
    WHERE u.franchise_id = ?
");
$summaryStmt->execute([$franchiseId]);
$summary = $summaryStmt->fetch();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Billing & Invoices - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
    <style>
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 sidebar p-0">
                <div class="p-3">
                    <div class="text-center text-white mb-4">
                        <i class="fas fa-store fa-3x mb-2"></i>
                        <h5><?php echo htmlspecialchars($franchiseDetails['full_name']); ?></h5>
                        <small><?php echo htmlspecialchars($franchiseDetails['franchise_code']); ?></small>
                    </div>

                    <nav class="nav flex-column">
                        <a class="nav-link text-white" href="dashboard.php">
                            <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                        </a>
                        <a class="nav-link text-white" href="users.php">
                            <i class="fas fa-users me-2"></i>Manage Users
                        </a>
                        <a class="nav-link text-white" href="products.php">
                            <i class="fas fa-box me-2"></i>Product Assignment
                        </a>
                        <a class="nav-link text-white" href="pv-transactions.php">
                            <i class="fas fa-chart-line me-2"></i>PV Transactions
                        </a>
                        <a class="nav-link text-white active" href="billing.php">
                            <i class="fas fa-file-invoice-dollar me-2"></i>Billing & Invoices
                        </a>
                        <a class="nav-link text-white" href="reports.php">
                            <i class="fas fa-chart-bar me-2"></i>Reports
                        </a>
                        <a class="nav-link text-white" href="profile.php">
                            <i class="fas fa-user me-2"></i>Profile
                        </a>
                        <hr class="text-white">
                        <a class="nav-link text-white" href="../logout.php">
                            <i class="fas fa-sign-out-alt me-2"></i>Logout
                        </a>
                    </nav>
                </div>
            </div>

            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2"><i class="fas fa-file-invoice-dollar me-2"></i>Billing & Invoices</h1>
                </div>

                <?php if ($error): ?>
                    <div class="alert alert-danger"><?php echo htmlspecialchars($error); ?></div>
                <?php endif; ?>

                <?php if ($success): ?>
                    <div class="alert alert-success"><?php echo htmlspecialchars($success); ?></div>
                <?php endif; ?>

                <!-- Summary Cards -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card text-white bg-primary">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="card-title">Total Orders</h6>
                                        <h3><?php echo number_format($summary['total_orders']); ?></h3>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-shopping-cart fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-white bg-success">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="card-title">Total Revenue</h6>
                                        <h3><?php echo formatCurrency($summary['total_revenue']); ?></h3>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-rupee-sign fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-white bg-info">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="card-title">Total PV</h6>
                                        <h3><?php echo formatPV($summary['total_pv']); ?></h3>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-star fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-white bg-warning">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="card-title">Confirmed Orders</h6>
                                        <h3><?php echo number_format($summary['confirmed_orders']); ?></h3>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-check-circle fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Filters -->
                <div class="card mb-4">
                    <div class="card-body">
                        <form method="GET" class="row g-3">
                            <div class="col-md-2">
                                <label for="status" class="form-label">Status</label>
                                <select class="form-select" id="status" name="status">
                                    <option value="all" <?php echo $status === 'all' ? 'selected' : ''; ?>>All Status</option>
                                    <option value="pending" <?php echo $status === 'pending' ? 'selected' : ''; ?>>Pending</option>
                                    <option value="confirmed" <?php echo $status === 'confirmed' ? 'selected' : ''; ?>>Confirmed</option>
                                    <option value="cancelled" <?php echo $status === 'cancelled' ? 'selected' : ''; ?>>Cancelled</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label for="date_from" class="form-label">From Date</label>
                                <input type="date" class="form-control" id="date_from" name="date_from" value="<?php echo htmlspecialchars($dateFrom); ?>">
                            </div>
                            <div class="col-md-2">
                                <label for="date_to" class="form-label">To Date</label>
                                <input type="date" class="form-control" id="date_to" name="date_to" value="<?php echo htmlspecialchars($dateTo); ?>">
                            </div>
                            <div class="col-md-4">
                                <label for="search" class="form-label">Search</label>
                                <input type="text" class="form-control" id="search" name="search" placeholder="Order ID, User Name, Product..." value="<?php echo htmlspecialchars($search); ?>">
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-grid">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search me-1"></i>Filter
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Orders Table -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Orders & Invoices</h5>
                    </div>
                    <div class="card-body">
                        <?php if (!empty($orders)): ?>
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>Order ID</th>
                                            <th>User</th>
                                            <th>Product</th>
                                            <th>Quantity</th>
                                            <th>Amount</th>
                                            <th>PV</th>
                                            <th>Status</th>
                                            <th>Date</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($orders as $order): ?>
                                            <tr>
                                                <td>
                                                    <strong><?php echo htmlspecialchars($order['order_id']); ?></strong>
                                                </td>
                                                <td>
                                                    <div>
                                                        <strong><?php echo htmlspecialchars($order['full_name']); ?></strong><br>
                                                        <small class="text-muted"><?php echo htmlspecialchars($order['user_code']); ?></small>
                                                    </div>
                                                </td>
                                                <td><?php echo htmlspecialchars($order['product_name']); ?></td>
                                                <td><?php echo number_format($order['quantity']); ?></td>
                                                <td><?php echo formatCurrency($order['total_amount']); ?></td>
                                                <td><?php echo formatPV($order['pv_amount']); ?></td>
                                                <td>
                                                    <?php
                                                    $statusClass = match($order['order_status']) {
                                                        'confirmed' => 'success',
                                                        'pending' => 'warning',
                                                        'cancelled' => 'danger',
                                                        default => 'secondary'
                                                    };
                                                    ?>
                                                    <span class="badge bg-<?php echo $statusClass; ?>">
                                                        <?php echo ucfirst($order['order_status']); ?>
                                                    </span>
                                                </td>
                                                <td><?php echo date('d M Y', strtotime($order['created_at'])); ?></td>
                                                <td>
                                                    <div class="btn-group btn-group-sm">
                                                        <a href="invoice.php?order_id=<?php echo urlencode($order['order_id']); ?>" 
                                                           class="btn btn-outline-primary" title="View Invoice">
                                                            <i class="fas fa-file-invoice"></i>
                                                        </a>
                                                        <a href="invoice.php?order_id=<?php echo urlencode($order['order_id']); ?>&download=1" 
                                                           class="btn btn-outline-success" title="Download PDF">
                                                            <i class="fas fa-download"></i>
                                                        </a>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>

                            <!-- Pagination -->
                            <?php if ($totalPages > 1): ?>
                                <nav aria-label="Orders pagination">
                                    <ul class="pagination justify-content-center">
                                        <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                                            <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                                                <a class="page-link" href="?page=<?php echo $i; ?>&status=<?php echo urlencode($status); ?>&date_from=<?php echo urlencode($dateFrom); ?>&date_to=<?php echo urlencode($dateTo); ?>&search=<?php echo urlencode($search); ?>">
                                                    <?php echo $i; ?>
                                                </a>
                                            </li>
                                        <?php endfor; ?>
                                    </ul>
                                </nav>
                            <?php endif; ?>
                        <?php else: ?>
                            <div class="text-center py-4">
                                <i class="fas fa-file-invoice fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">No orders found</h5>
                                <p class="text-muted">No orders match your current filters.</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
