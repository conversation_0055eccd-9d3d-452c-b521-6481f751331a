<?php
/**
 * Common Header File
 * MLM Binary Plan System
 * Include this file at the top of every page
 */

// Start output buffering
ob_start();

// Include configuration and database
require_once dirname(__DIR__) . '/config/database.php';
require_once dirname(__DIR__) . '/config/Connection.php';
require_once dirname(__DIR__) . '/config/config.php';

// Include common files
require_once dirname(__DIR__) . '/includes/session.php';
require_once dirname(__DIR__) . '/includes/functions.php';
require_once dirname(__DIR__) . '/includes/Validator.php';
require_once dirname(__DIR__) . '/includes/Response.php';

// Set error reporting based on environment
if (ENVIRONMENT === 'development') {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
} else {
    error_reporting(0);
    ini_set('display_errors', 0);
}

// Security headers
header('X-Content-Type-Options: nosniff');
header('X-Frame-Options: DENY');
header('X-XSS-Protection: 1; mode=block');

// CSRF token generation for forms
if (!isset($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}

/**
 * Get base URL
 */
function getBaseUrl() {
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    $host = $_SERVER['HTTP_HOST'];
    $path = dirname($_SERVER['SCRIPT_NAME']);
    return $protocol . '://' . $host . $path;
}

/**
 * Get current URL
 */
function getCurrentUrl() {
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    return $protocol . '://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'];
}

/**
 * Include CSS file
 */
function includeCSS($file) {
    $baseUrl = getBaseUrl();
    echo "<link rel='stylesheet' href='{$baseUrl}/assets/css/{$file}'>";
}

/**
 * Include JS file
 */
function includeJS($file) {
    $baseUrl = getBaseUrl();
    echo "<script src='{$baseUrl}/assets/js/{$file}'></script>";
}

/**
 * Display flash messages
 */
function displayFlashMessages() {
    $messages = getFlashMessages();
    if (!empty($messages)) {
        foreach ($messages as $message) {
            $alertClass = '';
            switch ($message['type']) {
                case 'success':
                    $alertClass = 'alert-success';
                    break;
                case 'error':
                    $alertClass = 'alert-danger';
                    break;
                case 'warning':
                    $alertClass = 'alert-warning';
                    break;
                case 'info':
                default:
                    $alertClass = 'alert-info';
                    break;
            }
            
            echo "<div class='alert {$alertClass} alert-dismissible fade show' role='alert'>";
            echo htmlspecialchars($message['message']);
            echo "<button type='button' class='btn-close' data-bs-dismiss='alert'></button>";
            echo "</div>";
        }
    }
}

/**
 * Check if current page is active
 */
function isActivePage($page) {
    $currentPage = basename($_SERVER['PHP_SELF']);
    return $currentPage === $page ? 'active' : '';
}

/**
 * Generate CSRF token input
 */
function csrfTokenInput() {
    return "<input type='hidden' name='csrf_token' value='" . $_SESSION['csrf_token'] . "'>";
}

/**
 * Verify CSRF token
 */
function verifyCsrfToken() {
    if (!isset($_POST['csrf_token']) || !hash_equals($_SESSION['csrf_token'], $_POST['csrf_token'])) {
        die('CSRF token mismatch');
    }
}
?>
