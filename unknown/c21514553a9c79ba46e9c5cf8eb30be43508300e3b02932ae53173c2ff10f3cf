<?php
/**
 * User Profile Page
 * MLM Binary Plan System
 */

require_once '../includes/header.php';
require_once '../includes/Auth.php';

// Require user authentication
Auth::requireUser();

$currentUser = Auth::user();
$userId = Auth::id();

$message = '';
$error = '';

// Handle profile update
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'update_profile') {
    verifyCsrfToken();
    
    $validator = new Validator($_POST);
    $validator->required('full_name', 'Full name is required')
             ->required('email', 'Email is required')
             ->email('email')
             ->required('phone', 'Phone number is required')
             ->phone('phone');
    
    // Check if email/phone is unique (excluding current user)
    $db = Database::getInstance();
    $emailStmt = $db->prepare("SELECT COUNT(*) FROM users WHERE email = ? AND user_id != ?");
    $emailStmt->execute([$_POST['email'], $userId]);
    if ($emailStmt->fetchColumn() > 0) {
        $validator->addError('email', 'Email already exists');
    }
    
    $phoneStmt = $db->prepare("SELECT COUNT(*) FROM users WHERE phone = ? AND user_id != ?");
    $phoneStmt->execute([$_POST['phone'], $userId]);
    if ($phoneStmt->fetchColumn() > 0) {
        $validator->addError('phone', 'Phone number already exists');
    }
    
    if ($validator->passes()) {
        try {
            $updateStmt = $db->prepare("UPDATE users SET full_name = ?, email = ?, phone = ?, address = ? WHERE user_id = ?");
            $updateStmt->execute([
                sanitizeInput($_POST['full_name']),
                sanitizeInput($_POST['email']),
                sanitizeInput($_POST['phone']),
                sanitizeInput($_POST['address']),
                $userId
            ]);
            
            // Update session data
            $currentUser['full_name'] = $_POST['full_name'];
            $currentUser['email'] = $_POST['email'];
            $currentUser['phone'] = $_POST['phone'];
            $_SESSION['user_data'] = $currentUser;
            
            setSuccessMessage('Profile updated successfully');
            Response::redirect('profile.php');
            
        } catch (Exception $e) {
            $error = 'Failed to update profile. Please try again.';
        }
    } else {
        $error = $validator->getFirstError();
    }
}

// Handle password change
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'change_password') {
    verifyCsrfToken();
    
    $currentPassword = $_POST['current_password'] ?? '';
    $newPassword = $_POST['new_password'] ?? '';
    $confirmPassword = $_POST['confirm_password'] ?? '';
    
    $validator = new Validator($_POST);
    $validator->required('current_password', 'Current password is required')
             ->required('new_password', 'New password is required')
             ->minLength('new_password', 6)
             ->required('confirm_password', 'Confirm password is required')
             ->matches('confirm_password', 'new_password', 'Passwords do not match');
    
    if ($validator->passes()) {
        try {
            $db = Database::getInstance();
            $userStmt = $db->prepare("SELECT password FROM users WHERE user_id = ?");
            $userStmt->execute([$userId]);
            $user = $userStmt->fetch();
            
            if (password_verify($currentPassword, $user['password'])) {
                $hashedPassword = password_hash($newPassword, PASSWORD_BCRYPT);
                $updateStmt = $db->prepare("UPDATE users SET password = ? WHERE user_id = ?");
                $updateStmt->execute([$hashedPassword, $userId]);
                
                setSuccessMessage('Password changed successfully');
                Response::redirect('profile.php');
            } else {
                $error = 'Current password is incorrect';
            }
        } catch (Exception $e) {
            $error = 'Failed to change password. Please try again.';
        }
    } else {
        $error = $validator->getFirstError();
    }
}

// Get current user details from database
$db = Database::getInstance();
$userStmt = $db->prepare("SELECT * FROM users WHERE user_id = ?");
$userStmt->execute([$userId]);
$userDetails = $userStmt->fetch();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Profile - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="dashboard.php">
                <i class="fas fa-network-wired me-2"></i><?php echo SITE_NAME; ?>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">
                            <i class="fas fa-tachometer-alt me-1"></i>Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="tree.php">
                            <i class="fas fa-sitemap me-1"></i>Binary Tree
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="wallet.php">
                            <i class="fas fa-wallet me-1"></i>Wallet
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="products.php">
                            <i class="fas fa-shopping-cart me-1"></i>Products
                        </a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle active" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-1"></i><?php echo htmlspecialchars($currentUser['full_name']); ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item active" href="profile.php"><i class="fas fa-user-edit me-2"></i>Profile</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../logout.php"><i class="fas fa-sign-out-alt me-2"></i>Logout</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Flash Messages -->
        <?php displayFlashMessages(); ?>
        
        <?php if ($error): ?>
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle me-2"></i><?php echo htmlspecialchars($error); ?>
            </div>
        <?php endif; ?>

        <div class="row">
            <!-- Profile Information -->
            <div class="col-md-6 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-user-edit me-2"></i>Profile Information</h5>
                    </div>
                    <div class="card-body">
                        <form method="POST">
                            <?php echo csrfTokenInput(); ?>
                            <input type="hidden" name="action" value="update_profile">
                            
                            <div class="mb-3">
                                <label for="user_id" class="form-label">User ID</label>
                                <input type="text" class="form-control" id="user_id" value="<?php echo htmlspecialchars($userId); ?>" readonly>
                            </div>
                            
                            <div class="mb-3">
                                <label for="username" class="form-label">Username</label>
                                <input type="text" class="form-control" id="username" value="<?php echo htmlspecialchars($userDetails['username']); ?>" readonly>
                            </div>
                            
                            <div class="mb-3">
                                <label for="full_name" class="form-label">Full Name *</label>
                                <input type="text" class="form-control" id="full_name" name="full_name" 
                                       value="<?php echo htmlspecialchars($userDetails['full_name']); ?>" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="email" class="form-label">Email *</label>
                                <input type="email" class="form-control" id="email" name="email" 
                                       value="<?php echo htmlspecialchars($userDetails['email']); ?>" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="phone" class="form-label">Phone Number *</label>
                                <input type="tel" class="form-control" id="phone" name="phone" 
                                       value="<?php echo htmlspecialchars($userDetails['phone']); ?>" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="address" class="form-label">Address</label>
                                <textarea class="form-control" id="address" name="address" rows="3"><?php echo htmlspecialchars($userDetails['address']); ?></textarea>
                            </div>
                            
                            <div class="mb-3">
                                <label for="sponsor_id" class="form-label">Sponsor ID</label>
                                <input type="text" class="form-control" id="sponsor_id" value="<?php echo htmlspecialchars($userDetails['sponsor_id']); ?>" readonly>
                            </div>
                            
                            <div class="mb-3">
                                <label for="registration_date" class="form-label">Registration Date</label>
                                <input type="text" class="form-control" id="registration_date" 
                                       value="<?php echo date('M d, Y', strtotime($userDetails['registration_date'])); ?>" readonly>
                            </div>
                            
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>Update Profile
                            </button>
                        </form>
                    </div>
                </div>
            </div>
            
            <!-- Change Password -->
            <div class="col-md-6 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-lock me-2"></i>Change Password</h5>
                    </div>
                    <div class="card-body">
                        <form method="POST">
                            <?php echo csrfTokenInput(); ?>
                            <input type="hidden" name="action" value="change_password">
                            
                            <div class="mb-3">
                                <label for="current_password" class="form-label">Current Password *</label>
                                <input type="password" class="form-control" id="current_password" name="current_password" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="new_password" class="form-label">New Password *</label>
                                <input type="password" class="form-control" id="new_password" name="new_password" 
                                       minlength="6" required>
                                <small class="text-muted">Minimum 6 characters</small>
                            </div>
                            
                            <div class="mb-3">
                                <label for="confirm_password" class="form-label">Confirm New Password *</label>
                                <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                            </div>
                            
                            <button type="submit" class="btn btn-warning">
                                <i class="fas fa-key me-2"></i>Change Password
                            </button>
                        </form>
                    </div>
                </div>
                
                <!-- Account Status -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>Account Status</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-6">
                                <strong>Status:</strong>
                            </div>
                            <div class="col-6">
                                <span class="status-badge status-<?php echo $userDetails['status']; ?>">
                                    <?php echo ucfirst($userDetails['status']); ?>
                                </span>
                            </div>
                        </div>
                        <hr>
                        <div class="row">
                            <div class="col-6">
                                <strong>Placement:</strong>
                            </div>
                            <div class="col-6">
                                <span class="badge bg-<?php echo $userDetails['placement_side'] === 'left' ? 'success' : 'warning'; ?>">
                                    <?php echo ucfirst($userDetails['placement_side']); ?> Side
                                </span>
                            </div>
                        </div>
                        <?php if ($userDetails['franchise_id']): ?>
                            <hr>
                            <div class="row">
                                <div class="col-6">
                                    <strong>Franchise:</strong>
                                </div>
                                <div class="col-6">
                                    <?php
                                    $franchiseStmt = $db->prepare("SELECT franchise_code, full_name FROM franchise WHERE id = ?");
                                    $franchiseStmt->execute([$userDetails['franchise_id']]);
                                    $franchise = $franchiseStmt->fetch();
                                    if ($franchise) {
                                        echo htmlspecialchars($franchise['franchise_code'] . ' - ' . $franchise['full_name']);
                                    }
                                    ?>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Password confirmation validation
        document.getElementById('confirm_password').addEventListener('input', function() {
            const newPassword = document.getElementById('new_password').value;
            const confirmPassword = this.value;
            
            if (newPassword !== confirmPassword) {
                this.setCustomValidity('Passwords do not match');
            } else {
                this.setCustomValidity('');
            }
        });
    </script>
</body>
</html>
