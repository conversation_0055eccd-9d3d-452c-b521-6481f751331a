<?php
/**
 * User Shopping Cart Page
 * MLM Binary Plan System
 */

require_once '../includes/header.php';
require_once '../includes/Auth.php';
require_once '../includes/Cart.php';
require_once '../includes/FileUpload.php';

// Require user authentication
Auth::requireUser();

$currentUser = Auth::user();
$userId = Auth::id();

$cart = new Cart();
$fileUpload = new FileUpload();
$error = '';
$success = '';

// Handle cart actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    verifyCsrfToken();
    
    $action = $_POST['action'] ?? '';
    
    try {
        if ($action === 'update_quantity') {
            $productId = (int) $_POST['product_id'];
            $quantity = (int) $_POST['quantity'];
            
            if ($cart->updateQuantity($productId, $quantity)) {
                $success = "Cart updated successfully!";
            } else {
                $error = "Failed to update cart.";
            }
            
        } elseif ($action === 'remove_item') {
            $productId = (int) $_POST['product_id'];
            
            if ($cart->removeProduct($productId)) {
                $success = "Item removed from cart!";
            } else {
                $error = "Failed to remove item.";
            }
            
        } elseif ($action === 'clear_cart') {
            $cart->clear();
            $success = "Cart cleared successfully!";
        }
        
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

// Handle add to cart from GET parameters
if (isset($_GET['add']) && is_numeric($_GET['add'])) {
    $productId = (int) $_GET['add'];
    $quantity = (int) ($_GET['quantity'] ?? 1);
    
    if ($cart->addProduct($productId, $quantity)) {
        $success = "Product added to cart!";
    } else {
        $error = "Failed to add product to cart.";
    }
}

// Validate cart items
$validation = $cart->validateItems();
if (!$validation['valid']) {
    $error = "Some items in your cart are no longer available and have been removed.";
}

$cartSummary = $cart->getSummary();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Shopping Cart - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="dashboard.php">
                <i class="fas fa-shopping-cart me-2"></i><?php echo SITE_NAME; ?>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">
                            <i class="fas fa-tachometer-alt me-1"></i>Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="products.php">
                            <i class="fas fa-box me-1"></i>Products
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="cart.php">
                            <i class="fas fa-shopping-cart me-1"></i>Cart 
                            <?php if (!$cartSummary['is_empty']): ?>
                                <span class="badge bg-danger"><?php echo $cartSummary['item_count']; ?></span>
                            <?php endif; ?>
                        </a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-1"></i><?php echo htmlspecialchars($currentUser['full_name']); ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="profile.php">Profile</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../logout.php">Logout</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Flash Messages -->
        <?php if ($error): ?>
            <div class="alert alert-danger alert-dismissible fade show">
                <i class="fas fa-exclamation-circle me-2"></i><?php echo $error; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>
        
        <?php if ($success): ?>
            <div class="alert alert-success alert-dismissible fade show">
                <i class="fas fa-check-circle me-2"></i><?php echo $success; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h4 class="mb-0">
                            <i class="fas fa-shopping-cart me-2"></i>Shopping Cart
                            <?php if (!$cartSummary['is_empty']): ?>
                                <span class="badge bg-primary"><?php echo $cartSummary['item_count']; ?> items</span>
                            <?php endif; ?>
                        </h4>
                    </div>
                </div>
            </div>
        </div>

        <?php if ($cartSummary['is_empty']): ?>
            <!-- Empty Cart -->
            <div class="row">
                <div class="col-12">
                    <div class="text-center py-5">
                        <i class="fas fa-shopping-cart fa-4x text-muted mb-3"></i>
                        <h4>Your cart is empty</h4>
                        <p class="text-muted">Add some products to get started!</p>
                        <a href="products.php" class="btn btn-primary">
                            <i class="fas fa-box me-2"></i>Browse Products
                        </a>
                    </div>
                </div>
            </div>
        <?php else: ?>
            <!-- Cart Items -->
            <div class="row">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">Cart Items</h5>
                        </div>
                        <div class="card-body">
                            <?php foreach ($cartSummary['items'] as $item): ?>
                                <div class="row align-items-center border-bottom py-3">
                                    <div class="col-md-2">
                                        <?php if ($item['image']): ?>
                                            <img src="../<?php echo htmlspecialchars($fileUpload->getFileUrl($item['image'])); ?>" 
                                                 alt="<?php echo htmlspecialchars($item['name']); ?>"
                                                 class="img-thumbnail" style="width: 80px; height: 80px; object-fit: cover;">
                                        <?php else: ?>
                                            <div class="bg-light d-flex align-items-center justify-content-center" 
                                                 style="width: 80px; height: 80px; border-radius: 4px;">
                                                <i class="fas fa-image text-muted"></i>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                    <div class="col-md-4">
                                        <h6><?php echo htmlspecialchars($item['name']); ?></h6>
                                        <small class="text-muted"><?php echo htmlspecialchars($item['product_code']); ?></small>
                                    </div>
                                    <div class="col-md-2">
                                        <strong>₹<?php echo number_format($item['price'], 2); ?></strong><br>
                                        <small class="text-muted"><?php echo formatPV($item['pv_value']); ?> PV</small>
                                    </div>
                                    <div class="col-md-2">
                                        <form method="POST" class="d-inline">
                                            <?php echo csrfTokenInput(); ?>
                                            <input type="hidden" name="action" value="update_quantity">
                                            <input type="hidden" name="product_id" value="<?php echo $item['product_id']; ?>">
                                            <div class="input-group input-group-sm">
                                                <input type="number" class="form-control" name="quantity" 
                                                       value="<?php echo $item['quantity']; ?>" min="1" max="10"
                                                       onchange="this.form.submit()">
                                            </div>
                                        </form>
                                    </div>
                                    <div class="col-md-2 text-end">
                                        <strong>₹<?php echo number_format($item['price'] * $item['quantity'], 2); ?></strong><br>
                                        <form method="POST" class="d-inline">
                                            <?php echo csrfTokenInput(); ?>
                                            <input type="hidden" name="action" value="remove_item">
                                            <input type="hidden" name="product_id" value="<?php echo $item['product_id']; ?>">
                                            <button type="submit" class="btn btn-sm btn-outline-danger" 
                                                    onclick="return confirm('Remove this item from cart?')">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <!-- Cart Summary -->
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">Order Summary</h5>
                        </div>
                        <div class="card-body">
                            <div class="d-flex justify-content-between mb-2">
                                <span>Items (<?php echo $cartSummary['item_count']; ?>):</span>
                                <span>₹<?php echo number_format($cartSummary['total_amount'], 2); ?></span>
                            </div>
                            <div class="d-flex justify-content-between mb-3">
                                <span>Total PV:</span>
                                <span class="text-primary"><?php echo formatPV($cartSummary['total_pv']); ?> PV</span>
                            </div>
                            <hr>
                            <div class="d-flex justify-content-between mb-3">
                                <strong>Total:</strong>
                                <strong>₹<?php echo number_format($cartSummary['total_amount'], 2); ?></strong>
                            </div>
                            
                            <div class="d-grid gap-2">
                                <a href="checkout.php" class="btn btn-success">
                                    <i class="fas fa-credit-card me-2"></i>Proceed to Checkout
                                </a>
                                <a href="products.php" class="btn btn-outline-primary">
                                    <i class="fas fa-plus me-2"></i>Add More Items
                                </a>
                                <form method="POST" class="d-inline">
                                    <?php echo csrfTokenInput(); ?>
                                    <input type="hidden" name="action" value="clear_cart">
                                    <button type="submit" class="btn btn-outline-danger w-100" 
                                            onclick="return confirm('Clear all items from cart?')">
                                        <i class="fas fa-trash me-2"></i>Clear Cart
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
