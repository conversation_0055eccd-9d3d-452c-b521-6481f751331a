<?php
/**
 * Health Check Endpoint
 * MLM Binary Plan System
 * 
 * This endpoint provides system health status for monitoring
 */

// Set content type
header('Content-Type: application/json');

// Include configuration
require_once 'config/database.php';
require_once 'config/Connection.php';

// Include production config if in production
if (ENVIRONMENT === 'production') {
    require_once 'config/production.php';
}

// Initialize health check
$healthCheck = [
    'status' => 'healthy',
    'timestamp' => date('Y-m-d H:i:s'),
    'environment' => ENVIRONMENT,
    'version' => '1.0.0',
    'checks' => []
];

try {
    // Database connectivity check
    try {
        $db = Database::getInstance();
        $stmt = $db->query('SELECT 1 as test');
        $result = $stmt->fetch();
        
        if ($result && $result['test'] == 1) {
            $healthCheck['checks']['database'] = [
                'status' => 'healthy',
                'message' => 'Database connection successful'
            ];
        } else {
            throw new Exception('Database query failed');
        }
    } catch (Exception $e) {
        $healthCheck['checks']['database'] = [
            'status' => 'unhealthy',
            'message' => 'Database connection failed: ' . $e->getMessage()
        ];
        $healthCheck['status'] = 'unhealthy';
    }
    
    // File system check
    try {
        $uploadsDir = 'uploads/';
        $logsDir = 'logs/';
        
        // Check if directories exist and are writable
        $fsChecks = [];
        
        if (is_dir($uploadsDir) && is_writable($uploadsDir)) {
            $fsChecks['uploads'] = 'writable';
        } else {
            $fsChecks['uploads'] = 'not writable';
            $healthCheck['status'] = 'unhealthy';
        }
        
        if (is_dir($logsDir) && is_writable($logsDir)) {
            $fsChecks['logs'] = 'writable';
        } else {
            $fsChecks['logs'] = 'not writable';
            $healthCheck['status'] = 'unhealthy';
        }
        
        // Check disk space
        $freeBytes = disk_free_space('.');
        $totalBytes = disk_total_space('.');
        $usedPercent = (($totalBytes - $freeBytes) / $totalBytes) * 100;
        
        $fsChecks['disk_usage'] = round($usedPercent, 2) . '%';
        
        if ($usedPercent > 90) {
            $healthCheck['status'] = 'unhealthy';
            $fsChecks['disk_status'] = 'critical';
        } elseif ($usedPercent > 80) {
            $fsChecks['disk_status'] = 'warning';
        } else {
            $fsChecks['disk_status'] = 'healthy';
        }
        
        $healthCheck['checks']['filesystem'] = [
            'status' => ($healthCheck['status'] === 'unhealthy') ? 'unhealthy' : 'healthy',
            'details' => $fsChecks
        ];
        
    } catch (Exception $e) {
        $healthCheck['checks']['filesystem'] = [
            'status' => 'unhealthy',
            'message' => 'Filesystem check failed: ' . $e->getMessage()
        ];
        $healthCheck['status'] = 'unhealthy';
    }
    
    // Memory usage check
    try {
        $memoryUsage = memory_get_usage(true);
        $memoryLimit = ini_get('memory_limit');
        
        // Convert memory limit to bytes
        $memoryLimitBytes = 0;
        if (preg_match('/^(\d+)(.)$/', $memoryLimit, $matches)) {
            $memoryLimitBytes = $matches[1];
            switch (strtoupper($matches[2])) {
                case 'G':
                    $memoryLimitBytes *= 1024;
                case 'M':
                    $memoryLimitBytes *= 1024;
                case 'K':
                    $memoryLimitBytes *= 1024;
            }
        }
        
        $memoryUsagePercent = ($memoryLimitBytes > 0) ? ($memoryUsage / $memoryLimitBytes) * 100 : 0;
        
        $healthCheck['checks']['memory'] = [
            'status' => ($memoryUsagePercent > 90) ? 'warning' : 'healthy',
            'usage' => round($memoryUsage / 1024 / 1024, 2) . ' MB',
            'limit' => $memoryLimit,
            'usage_percent' => round($memoryUsagePercent, 2) . '%'
        ];
        
    } catch (Exception $e) {
        $healthCheck['checks']['memory'] = [
            'status' => 'unhealthy',
            'message' => 'Memory check failed: ' . $e->getMessage()
        ];
    }
    
    // PHP configuration check
    try {
        $phpChecks = [
            'version' => PHP_VERSION,
            'extensions' => []
        ];
        
        // Check required extensions
        $requiredExtensions = ['pdo', 'pdo_mysql', 'json', 'mbstring', 'openssl', 'curl'];
        foreach ($requiredExtensions as $ext) {
            $phpChecks['extensions'][$ext] = extension_loaded($ext) ? 'loaded' : 'missing';
            if (!extension_loaded($ext)) {
                $healthCheck['status'] = 'unhealthy';
            }
        }
        
        // Check optional extensions
        $optionalExtensions = ['gd', 'zip', 'redis', 'memcached'];
        foreach ($optionalExtensions as $ext) {
            if (extension_loaded($ext)) {
                $phpChecks['extensions'][$ext] = 'loaded';
            }
        }
        
        $healthCheck['checks']['php'] = [
            'status' => ($healthCheck['status'] === 'unhealthy') ? 'unhealthy' : 'healthy',
            'details' => $phpChecks
        ];
        
    } catch (Exception $e) {
        $healthCheck['checks']['php'] = [
            'status' => 'unhealthy',
            'message' => 'PHP check failed: ' . $e->getMessage()
        ];
        $healthCheck['status'] = 'unhealthy';
    }
    
    // Application-specific checks
    try {
        $appChecks = [];
        
        // Check if critical tables exist
        $criticalTables = ['users', 'admin', 'franchise', 'products', 'pv_transactions', 'wallet'];
        $missingTables = [];
        
        foreach ($criticalTables as $table) {
            try {
                $stmt = $db->query("SHOW TABLES LIKE '$table'");
                if (!$stmt->fetch()) {
                    $missingTables[] = $table;
                }
            } catch (Exception $e) {
                $missingTables[] = $table;
            }
        }
        
        if (!empty($missingTables)) {
            $appChecks['database_schema'] = 'missing tables: ' . implode(', ', $missingTables);
            $healthCheck['status'] = 'unhealthy';
        } else {
            $appChecks['database_schema'] = 'complete';
        }
        
        // Check configuration files
        $configFiles = ['config/database.php', 'config/Connection.php', 'config/config.php'];
        $missingConfigs = [];
        
        foreach ($configFiles as $file) {
            if (!file_exists($file)) {
                $missingConfigs[] = $file;
            }
        }
        
        if (!empty($missingConfigs)) {
            $appChecks['configuration'] = 'missing files: ' . implode(', ', $missingConfigs);
            $healthCheck['status'] = 'unhealthy';
        } else {
            $appChecks['configuration'] = 'complete';
        }
        
        $healthCheck['checks']['application'] = [
            'status' => ($healthCheck['status'] === 'unhealthy') ? 'unhealthy' : 'healthy',
            'details' => $appChecks
        ];
        
    } catch (Exception $e) {
        $healthCheck['checks']['application'] = [
            'status' => 'unhealthy',
            'message' => 'Application check failed: ' . $e->getMessage()
        ];
        $healthCheck['status'] = 'unhealthy';
    }
    
    // Cache check (if enabled in production)
    if (ENVIRONMENT === 'production' && defined('PROD_CACHE_ENABLED') && PROD_CACHE_ENABLED) {
        try {
            $cache = ProductionCache::getInstance();
            $testKey = 'health_check_' . time();
            $testValue = 'test_value';
            
            $cache->set($testKey, $testValue, 60);
            $retrievedValue = $cache->get($testKey);
            $cache->delete($testKey);
            
            if ($retrievedValue === $testValue) {
                $healthCheck['checks']['cache'] = [
                    'status' => 'healthy',
                    'message' => 'Cache read/write successful'
                ];
            } else {
                throw new Exception('Cache read/write failed');
            }
        } catch (Exception $e) {
            $healthCheck['checks']['cache'] = [
                'status' => 'unhealthy',
                'message' => 'Cache check failed: ' . $e->getMessage()
            ];
            $healthCheck['status'] = 'unhealthy';
        }
    }
    
} catch (Exception $e) {
    $healthCheck['status'] = 'unhealthy';
    $healthCheck['error'] = 'Health check failed: ' . $e->getMessage();
}

// Set appropriate HTTP status code
if ($healthCheck['status'] === 'healthy') {
    http_response_code(200);
} else {
    http_response_code(503); // Service Unavailable
}

// Output health check result
echo json_encode($healthCheck, JSON_PRETTY_PRINT);
?>
