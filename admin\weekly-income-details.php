<?php
/**
 * Weekly Income Report Details - Admin Panel
 * MLM Binary Plan System
 */

require_once '../includes/header.php';
require_once '../includes/Auth.php';
require_once '../includes/PVSystem.php';
require_once '../config/config.php';

// Require admin authentication
Auth::requireAdmin();

$currentUser = Auth::user();
$adminId = Auth::id();

// Get week parameter
$weekStart = $_GET['week'] ?? '';
if (!$weekStart) {
    header("Location: weekly-income-reports.php");
    exit();
}

// Calculate week end date
$weekEnd = date('Y-m-d', strtotime($weekStart . ' +6 days'));

// Get database instance
$db = Database::getInstance();
$config = Config::getInstance();

// Get weekly report summary
$reportStmt = $db->prepare("SELECT * FROM weekly_income_reports WHERE week_start_date = ?");
$reportStmt->execute([$weekStart]);
$report = $reportStmt->fetch();

if (!$report) {
    header("Location: weekly-income-reports.php");
    exit();
}

// Get detailed income logs for this week
$logsStmt = $db->prepare("
    SELECT wil.*, u.full_name, u.email 
    FROM weekly_income_logs wil
    JOIN users u ON wil.user_id = u.user_id
    WHERE wil.week_start_date = ?
    ORDER BY wil.income_amount DESC, u.full_name
");
$logsStmt->execute([$weekStart]);
$incomeLogs = $logsStmt->fetchAll();

// Calculate statistics
$totalUsers = count($incomeLogs);
$usersWithIncome = count(array_filter($incomeLogs, function($log) { return $log['income_amount'] > 0; }));
$usersWithCapping = count(array_filter($incomeLogs, function($log) { return $log['weekly_capping_applied'] > 0; }));
$avgIncome = $totalUsers > 0 ? $report['total_income_distributed'] / $usersWithIncome : 0;
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Weekly Income Details - <?php echo SITE_NAME; ?> Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
</head>
<body>
    <?php include 'includes/navigation.php'; ?>

    <div class="container-fluid mt-4">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2>
                            <i class="fas fa-chart-line me-2"></i>
                            Weekly Income Details
                        </h2>
                        <p class="text-muted mb-0">
                            <?php echo date('M d', strtotime($weekStart)); ?> - <?php echo date('M d, Y', strtotime($weekEnd)); ?>
                        </p>
                    </div>
                    <a href="weekly-income-reports.php" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-1"></i>Back to Reports
                    </a>
                </div>
            </div>
        </div>

                <!-- Summary Cards -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card bg-primary text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="card-title">Total Users</h6>
                                        <h3><?php echo $totalUsers; ?></h3>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-users fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-success text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="card-title">Users with Income</h6>
                                        <h3><?php echo $usersWithIncome; ?></h3>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-hand-holding-usd fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-info text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="card-title">Total Income</h6>
                                        <h3>₹<?php echo number_format($report['total_income_distributed'], 0); ?></h3>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-coins fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-warning text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="card-title">Capping Applied</h6>
                                        <h3>₹<?php echo number_format($report['total_capping_applied'], 0); ?></h3>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-exclamation-triangle fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Additional Statistics -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-body">
                                <h6 class="card-title">Income Statistics</h6>
                                <div class="row">
                                    <div class="col-6">
                                        <small class="text-muted">Average Income</small>
                                        <div class="h5">₹<?php echo number_format($avgIncome, 2); ?></div>
                                    </div>
                                    <div class="col-6">
                                        <small class="text-muted">Users with Capping</small>
                                        <div class="h5"><?php echo $usersWithCapping; ?></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-body">
                                <h6 class="card-title">Report Status</h6>
                                <div class="row">
                                    <div class="col-6">
                                        <small class="text-muted">Generated</small>
                                        <div class="h6"><?php echo date('M d, Y H:i', strtotime($report['report_generated_at'])); ?></div>
                                    </div>
                                    <div class="col-6">
                                        <small class="text-muted">Status</small>
                                        <div>
                                            <?php
                                            $statusClass = [
                                                'generated' => 'bg-warning',
                                                'sent' => 'bg-success',
                                                'failed' => 'bg-danger'
                                            ];
                                            ?>
                                            <span class="badge <?php echo $statusClass[$report['report_status']] ?? 'bg-secondary'; ?>">
                                                <?php echo ucfirst($report['report_status']); ?>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Detailed Income Logs -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">User Income Details</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>User</th>
                                        <th>Left PV</th>
                                        <th>Right PV</th>
                                        <th>Matched PV</th>
                                        <th>Income</th>
                                        <th>Capping</th>
                                        <th>Carry Forward</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($incomeLogs as $log): ?>
                                        <tr>
                                            <td>
                                                <div>
                                                    <strong><?php echo htmlspecialchars($log['full_name']); ?></strong>
                                                    <br>
                                                    <small class="text-muted"><?php echo htmlspecialchars($log['user_id']); ?></small>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="badge bg-info"><?php echo number_format($log['left_pv'], 0); ?></span>
                                            </td>
                                            <td>
                                                <span class="badge bg-info"><?php echo number_format($log['right_pv'], 0); ?></span>
                                            </td>
                                            <td>
                                                <strong><?php echo number_format($log['matched_pv'], 0); ?></strong>
                                            </td>
                                            <td>
                                                <?php if ($log['income_amount'] > 0): ?>
                                                    <strong class="text-success">₹<?php echo number_format($log['income_amount'], 2); ?></strong>
                                                <?php else: ?>
                                                    <span class="text-muted">₹0.00</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if ($log['weekly_capping_applied'] > 0): ?>
                                                    <span class="text-warning">₹<?php echo number_format($log['weekly_capping_applied'], 2); ?></span>
                                                <?php else: ?>
                                                    <span class="text-muted">-</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <small>
                                                    L: <?php echo number_format($log['carry_forward_left'], 0); ?><br>
                                                    R: <?php echo number_format($log['carry_forward_right'], 0); ?>
                                                </small>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
