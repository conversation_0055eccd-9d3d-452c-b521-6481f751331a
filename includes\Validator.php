<?php
/**
 * Input Validation Class
 * MLM Binary Plan System
 */

class Validator {
    private $errors = [];
    private $data = [];
    
    public function __construct($data = []) {
        $this->data = $data;
    }
    
    /**
     * Validate required field
     */
    public function required($field, $message = null) {
        if (!isset($this->data[$field]) || empty(trim($this->data[$field]))) {
            $this->errors[$field] = $message ?? ucfirst($field) . ' is required';
        }
        return $this;
    }
    
    /**
     * Validate email format
     */
    public function email($field, $message = null) {
        if (isset($this->data[$field]) && !empty($this->data[$field])) {
            if (!filter_var($this->data[$field], FILTER_VALIDATE_EMAIL)) {
                $this->errors[$field] = $message ?? 'Invalid email format';
            }
        }
        return $this;
    }
    
    /**
     * Validate phone number (Indian format)
     */
    public function phone($field, $message = null) {
        if (isset($this->data[$field]) && !empty($this->data[$field])) {
            $phone = preg_replace('/[^0-9]/', '', $this->data[$field]);
            if (!preg_match('/^[6-9]\d{9}$/', $phone)) {
                $this->errors[$field] = $message ?? 'Invalid phone number format';
            }
        }
        return $this;
    }
    
    /**
     * Validate minimum length
     */
    public function minLength($field, $length, $message = null) {
        if (isset($this->data[$field]) && !empty($this->data[$field])) {
            if (strlen($this->data[$field]) < $length) {
                $this->errors[$field] = $message ?? ucfirst($field) . " must be at least {$length} characters";
            }
        }
        return $this;
    }
    
    /**
     * Validate maximum length
     */
    public function maxLength($field, $length, $message = null) {
        if (isset($this->data[$field]) && !empty($this->data[$field])) {
            if (strlen($this->data[$field]) > $length) {
                $this->errors[$field] = $message ?? ucfirst($field) . " must not exceed {$length} characters";
            }
        }
        return $this;
    }
    
    /**
     * Validate numeric value
     */
    public function numeric($field, $message = null) {
        if (isset($this->data[$field]) && !empty($this->data[$field])) {
            if (!is_numeric($this->data[$field])) {
                $this->errors[$field] = $message ?? ucfirst($field) . ' must be a number';
            }
        }
        return $this;
    }
    
    /**
     * Validate minimum value
     */
    public function min($field, $min, $message = null) {
        if (isset($this->data[$field]) && !empty($this->data[$field])) {
            if (is_numeric($this->data[$field]) && $this->data[$field] < $min) {
                $this->errors[$field] = $message ?? ucfirst($field) . " must be at least {$min}";
            }
        }
        return $this;
    }
    
    /**
     * Validate maximum value
     */
    public function max($field, $max, $message = null) {
        if (isset($this->data[$field]) && !empty($this->data[$field])) {
            if (is_numeric($this->data[$field]) && $this->data[$field] > $max) {
                $this->errors[$field] = $message ?? ucfirst($field) . " must not exceed {$max}";
            }
        }
        return $this;
    }
    
    /**
     * Validate that field matches another field
     */
    public function matches($field, $matchField, $message = null) {
        if (isset($this->data[$field]) && isset($this->data[$matchField])) {
            if ($this->data[$field] !== $this->data[$matchField]) {
                $this->errors[$field] = $message ?? ucfirst($field) . ' does not match ' . ucfirst($matchField);
            }
        }
        return $this;
    }
    
    /**
     * Validate unique value in database
     */
    public function unique($field, $table, $column = null, $excludeId = null, $message = null) {
        if (isset($this->data[$field]) && !empty($this->data[$field])) {
            $column = $column ?? $field;
            $db = Database::getInstance();
            
            $sql = "SELECT COUNT(*) FROM {$table} WHERE {$column} = ?";
            $params = [$this->data[$field]];
            
            if ($excludeId) {
                $sql .= " AND id != ?";
                $params[] = $excludeId;
            }
            
            $stmt = $db->prepare($sql);
            $stmt->execute($params);
            
            if ($stmt->fetchColumn() > 0) {
                $this->errors[$field] = $message ?? ucfirst($field) . ' already exists';
            }
        }
        return $this;
    }
    
    /**
     * Validate that value exists in database
     */
    public function exists($field, $table, $column = null, $message = null) {
        if (isset($this->data[$field]) && !empty($this->data[$field])) {
            $column = $column ?? $field;
            $db = Database::getInstance();
            
            $stmt = $db->prepare("SELECT COUNT(*) FROM {$table} WHERE {$column} = ?");
            $stmt->execute([$this->data[$field]]);
            
            if ($stmt->fetchColumn() == 0) {
                $this->errors[$field] = $message ?? ucfirst($field) . ' does not exist';
            }
        }
        return $this;
    }
    
    /**
     * Validate using custom callback
     */
    public function custom($field, $callback, $message = null) {
        if (isset($this->data[$field])) {
            if (!call_user_func($callback, $this->data[$field])) {
                $this->errors[$field] = $message ?? ucfirst($field) . ' is invalid';
            }
        }
        return $this;
    }
    
    /**
     * Check if validation passed
     */
    public function passes() {
        return empty($this->errors);
    }
    
    /**
     * Check if validation failed
     */
    public function fails() {
        return !$this->passes();
    }
    
    /**
     * Get all errors
     */
    public function getErrors() {
        return $this->errors;
    }
    
    /**
     * Get first error for a field
     */
    public function getError($field) {
        return $this->errors[$field] ?? null;
    }
    
    /**
     * Get first error message
     */
    public function getFirstError() {
        return !empty($this->errors) ? reset($this->errors) : null;
    }
    
    /**
     * Add custom error
     */
    public function addError($field, $message) {
        $this->errors[$field] = $message;
        return $this;
    }
}
?>
