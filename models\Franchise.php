<?php
/**
 * Franchise Model
 * MLM Binary Plan System
 */

require_once 'BaseModel.php';

class Franchise extends BaseModel {
    protected $table = 'franchise';
    protected $primaryKey = 'id';
    protected $fillable = [
        'franchise_code', 'username', 'email', 'password', 'full_name', 
        'phone', 'address', 'commission_rate', 'status', 'created_by'
    ];
    protected $hidden = ['password'];
    
    /**
     * Find franchise by franchise code
     * 
     * @param string $franchiseCode Franchise code
     * @return array|false Franchise data or false if not found
     */
    public function findByFranchiseCode($franchiseCode) {
        return $this->findBy('franchise_code', $franchiseCode);
    }
    
    /**
     * Find franchise by username
     * 
     * @param string $username Username
     * @return array|false Franchise data or false if not found
     */
    public function findByUsername($username) {
        return $this->findBy('username', $username);
    }
    
    /**
     * Find franchise by email
     * 
     * @param string $email Email
     * @return array|false Franchise data or false if not found
     */
    public function findByEmail($email) {
        return $this->findBy('email', $email);
    }
    
    /**
     * Authenticate franchise
     * 
     * @param string $username Username or email
     * @param string $password Password
     * @return array|false Franchise data or false if authentication fails
     */
    public function authenticate($username, $password) {
        // Check if username is an email
        $isEmail = filter_var($username, FILTER_VALIDATE_EMAIL);
        
        // Find franchise by username or email
        if ($isEmail) {
            $franchise = $this->findByEmail($username);
        } else {
            $franchise = $this->findByUsername($username);
        }
        
        // Check if franchise exists
        if (!$franchise) {
            return false;
        }
        
        // Check if franchise is active
        if ($franchise['status'] !== 'active') {
            return false;
        }
        
        // Check password (plain text as requested by user)
        if ($franchise['password'] !== $password) {
            return false;
        }
        
        // Remove password from franchise data
        unset($franchise['password']);
        
        return $franchise;
    }
    
    /**
     * Create a new franchise
     * 
     * @param array $data Franchise data
     * @return int|false Franchise ID or false on failure
     */
    public function createFranchise(array $data) {
        // Generate franchise code if not provided
        if (empty($data['franchise_code'])) {
            $data['franchise_code'] = $this->generateFranchiseCode();
        }
        
        // Set default status if not provided
        if (empty($data['status'])) {
            $data['status'] = 'active';
        }
        
        // Set default commission rate if not provided
        if (empty($data['commission_rate'])) {
            $data['commission_rate'] = 5.00;
        }
        
        return $this->create($data);
    }
    
    /**
     * Generate a unique franchise code
     * 
     * @return string Franchise code
     */
    private function generateFranchiseCode() {
        $prefix = 'FRN';
        $timestamp = time();
        $random = mt_rand(100, 999);
        
        return $prefix . $timestamp . $random;
    }
    
    /**
     * Get franchise users
     * 
     * @param int $franchiseId Franchise ID
     * @param int $limit Maximum number of users to return
     * @param int $offset Offset for pagination
     * @return array Franchise users
     */
    public function getFranchiseUsers($franchiseId, $limit = 1000, $offset = 0) {
        $stmt = $this->db->prepare("
            SELECT * FROM users 
            WHERE franchise_id = ? 
            ORDER BY registration_date DESC 
            LIMIT ? OFFSET ?
        ");
        $stmt->execute([$franchiseId, $limit, $offset]);
        return $stmt->fetchAll();
    }
    
    /**
     * Count franchise users
     * 
     * @param int $franchiseId Franchise ID
     * @return int Number of franchise users
     */
    public function countFranchiseUsers($franchiseId) {
        $stmt = $this->db->prepare("SELECT COUNT(*) as count FROM users WHERE franchise_id = ?");
        $stmt->execute([$franchiseId]);
        $result = $stmt->fetch();
        return (int) $result['count'];
    }
    
    /**
     * Get franchise statistics
     * 
     * @param int $franchiseId Franchise ID
     * @return array Franchise statistics
     */
    public function getFranchiseStats($franchiseId) {
        $stats = [];
        
        // Total users
        $stats['total_users'] = $this->countFranchiseUsers($franchiseId);
        
        // Active users
        $stmt = $this->db->prepare("SELECT COUNT(*) as count FROM users WHERE franchise_id = ? AND status = 'active'");
        $stmt->execute([$franchiseId]);
        $result = $stmt->fetch();
        $stats['active_users'] = (int) $result['count'];
        
        // Total PV
        $stmt = $this->db->prepare("
            SELECT SUM(pt.pv_amount) as total_pv 
            FROM pv_transactions pt 
            JOIN users u ON pt.user_id = u.user_id 
            WHERE u.franchise_id = ?
        ");
        $stmt->execute([$franchiseId]);
        $result = $stmt->fetch();
        $stats['total_pv'] = (float) ($result['total_pv'] ?? 0);
        
        // Total orders
        $stmt = $this->db->prepare("
            SELECT COUNT(*) as count, SUM(total_amount) as total_amount 
            FROM purchase_orders po 
            JOIN users u ON po.user_id = u.user_id 
            WHERE u.franchise_id = ? 
            AND po.order_status = 'confirmed'
        ");
        $stmt->execute([$franchiseId]);
        $result = $stmt->fetch();
        $stats['total_orders'] = (int) $result['count'];
        $stats['total_order_amount'] = (float) ($result['total_amount'] ?? 0);
        
        // Commission earned
        $commissionRate = $this->getCommissionRate($franchiseId);
        $stats['commission_rate'] = $commissionRate;
        $stats['commission_earned'] = $stats['total_order_amount'] * ($commissionRate / 100);
        
        return $stats;
    }
    
    /**
     * Get franchise commission rate
     * 
     * @param int $franchiseId Franchise ID
     * @return float Commission rate
     */
    public function getCommissionRate($franchiseId) {
        $franchise = $this->find($franchiseId);
        return (float) ($franchise['commission_rate'] ?? 5.00);
    }
    
    /**
     * Get franchise sales data
     * 
     * @param int $franchiseId Franchise ID
     * @param int $days Number of days to get data for
     * @return array Franchise sales data
     */
    public function getFranchiseSalesData($franchiseId, $days = 30) {
        $stmt = $this->db->prepare("
            SELECT DATE(po.created_at) as date, COUNT(*) as order_count, SUM(po.total_amount) as total_amount
            FROM purchase_orders po 
            JOIN users u ON po.user_id = u.user_id 
            WHERE u.franchise_id = ? 
            AND po.order_status = 'confirmed'
            AND po.created_at >= DATE_SUB(CURDATE(), INTERVAL ? DAY)
            GROUP BY DATE(po.created_at)
            ORDER BY date ASC
        ");
        $stmt->execute([$franchiseId, $days]);
        return $stmt->fetchAll();
    }
    
    /**
     * Get franchise user growth data
     * 
     * @param int $franchiseId Franchise ID
     * @param int $days Number of days to get data for
     * @return array Franchise user growth data
     */
    public function getFranchiseUserGrowthData($franchiseId, $days = 30) {
        $stmt = $this->db->prepare("
            SELECT DATE(registration_date) as date, COUNT(*) as count 
            FROM users 
            WHERE franchise_id = ? 
            AND registration_date >= DATE_SUB(CURDATE(), INTERVAL ? DAY)
            GROUP BY DATE(registration_date)
            ORDER BY date ASC
        ");
        $stmt->execute([$franchiseId, $days]);
        return $stmt->fetchAll();
    }
    
    /**
     * Get franchise PV data
     * 
     * @param int $franchiseId Franchise ID
     * @param int $days Number of days to get data for
     * @return array Franchise PV data
     */
    public function getFranchisePVData($franchiseId, $days = 30) {
        $stmt = $this->db->prepare("
            SELECT DATE(pt.created_at) as date, SUM(pt.pv_amount) as total_pv
            FROM pv_transactions pt 
            JOIN users u ON pt.user_id = u.user_id 
            WHERE u.franchise_id = ? 
            AND pt.created_at >= DATE_SUB(CURDATE(), INTERVAL ? DAY)
            GROUP BY DATE(pt.created_at)
            ORDER BY date ASC
        ");
        $stmt->execute([$franchiseId, $days]);
        return $stmt->fetchAll();
    }
    
    /**
     * Get franchise top products
     * 
     * @param int $franchiseId Franchise ID
     * @param int $limit Maximum number of products to return
     * @return array Franchise top products
     */
    public function getFranchiseTopProducts($franchiseId, $limit = 10) {
        $stmt = $this->db->prepare("
            SELECT p.*, COUNT(po.id) as order_count, SUM(po.quantity) as total_quantity
            FROM products p
            JOIN purchase_orders po ON p.id = po.product_id
            JOIN users u ON po.user_id = u.user_id
            WHERE u.franchise_id = ?
            AND po.order_status = 'confirmed'
            GROUP BY p.id
            ORDER BY order_count DESC, total_quantity DESC
            LIMIT ?
        ");
        $stmt->execute([$franchiseId, $limit]);
        return $stmt->fetchAll();
    }
}
?>
