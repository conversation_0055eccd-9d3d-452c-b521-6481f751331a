<?php
/**
 * Shopping Cart Management Class
 * MLM Binary Plan System
 */

class Cart {
    
    private $cartKey = 'shopping_cart';
    
    /**
     * Add product to cart
     */
    public function addProduct($productId, $quantity = 1) {
        if (!isset($_SESSION[$this->cartKey])) {
            $_SESSION[$this->cartKey] = [];
        }
        
        $productId = (int) $productId;
        $quantity = (int) $quantity;
        
        if ($quantity <= 0) {
            return false;
        }
        
        // If product already exists in cart, update quantity
        if (isset($_SESSION[$this->cartKey][$productId])) {
            $_SESSION[$this->cartKey][$productId]['quantity'] += $quantity;
        } else {
            // Get product details from database
            $db = Database::getInstance();
            $stmt = $db->prepare("SELECT * FROM products WHERE id = ? AND status = 'active'");
            $stmt->execute([$productId]);
            $product = $stmt->fetch();
            
            if (!$product) {
                return false;
            }
            
            $_SESSION[$this->cartKey][$productId] = [
                'product_id' => $productId,
                'product_code' => $product['product_code'],
                'name' => $product['name'],
                'price' => $product['price'],
                'pv_value' => $product['pv_value'],
                'image' => $product['image'],
                'quantity' => $quantity
            ];
        }
        
        return true;
    }
    
    /**
     * Remove product from cart
     */
    public function removeProduct($productId) {
        $productId = (int) $productId;
        
        if (isset($_SESSION[$this->cartKey][$productId])) {
            unset($_SESSION[$this->cartKey][$productId]);
            return true;
        }
        
        return false;
    }
    
    /**
     * Update product quantity in cart
     */
    public function updateQuantity($productId, $quantity) {
        $productId = (int) $productId;
        $quantity = (int) $quantity;
        
        if ($quantity <= 0) {
            return $this->removeProduct($productId);
        }
        
        if (isset($_SESSION[$this->cartKey][$productId])) {
            $_SESSION[$this->cartKey][$productId]['quantity'] = $quantity;
            return true;
        }
        
        return false;
    }
    
    /**
     * Get all cart items
     */
    public function getItems() {
        return $_SESSION[$this->cartKey] ?? [];
    }
    
    /**
     * Get cart item count
     */
    public function getItemCount() {
        $items = $this->getItems();
        $count = 0;
        
        foreach ($items as $item) {
            $count += $item['quantity'];
        }
        
        return $count;
    }
    
    /**
     * Get cart total amount
     */
    public function getTotalAmount() {
        $items = $this->getItems();
        $total = 0;
        
        foreach ($items as $item) {
            $total += $item['price'] * $item['quantity'];
        }
        
        return $total;
    }
    
    /**
     * Get cart total PV
     */
    public function getTotalPV() {
        $items = $this->getItems();
        $totalPV = 0;
        
        foreach ($items as $item) {
            $totalPV += $item['pv_value'] * $item['quantity'];
        }
        
        return $totalPV;
    }
    
    /**
     * Check if cart is empty
     */
    public function isEmpty() {
        $items = $this->getItems();
        return empty($items);
    }
    
    /**
     * Clear cart
     */
    public function clear() {
        unset($_SESSION[$this->cartKey]);
    }
    
    /**
     * Get cart summary
     */
    public function getSummary() {
        return [
            'items' => $this->getItems(),
            'item_count' => $this->getItemCount(),
            'total_amount' => $this->getTotalAmount(),
            'total_pv' => $this->getTotalPV(),
            'is_empty' => $this->isEmpty()
        ];
    }
    
    /**
     * Validate cart items (check if products still exist and are active)
     */
    public function validateItems() {
        $items = $this->getItems();
        $validItems = [];
        $removedItems = [];
        
        if (empty($items)) {
            return ['valid' => true, 'removed_items' => []];
        }
        
        $db = Database::getInstance();
        
        foreach ($items as $productId => $item) {
            $stmt = $db->prepare("SELECT * FROM products WHERE id = ? AND status = 'active'");
            $stmt->execute([$productId]);
            $product = $stmt->fetch();
            
            if ($product) {
                // Update item with current product data
                $validItems[$productId] = [
                    'product_id' => $productId,
                    'product_code' => $product['product_code'],
                    'name' => $product['name'],
                    'price' => $product['price'],
                    'pv_value' => $product['pv_value'],
                    'image' => $product['image'],
                    'quantity' => $item['quantity']
                ];
            } else {
                $removedItems[] = $item;
            }
        }
        
        // Update cart with valid items only
        $_SESSION[$this->cartKey] = $validItems;
        
        return [
            'valid' => empty($removedItems),
            'removed_items' => $removedItems
        ];
    }
}
?>
