<?php
/**
 * Email Service Class
 * MLM Binary Plan System
 */

class EmailService {
    private $config;
    
    public function __construct() {
        $this->config = Config::getInstance();
    }
    
    /**
     * Send weekly income report email to admin
     */
    public function sendWeeklyIncomeReport($weekStartDate, $weekEndDate, $reportData) {
        try {
            $adminEmail = $this->config->get('admin_notification_email', '<EMAIL>');
            $subject = "Weekly Income Report - " . date('M d, Y', strtotime($weekStartDate)) . " to " . date('M d, Y', strtotime($weekEndDate));
            
            // Create HTML email content
            $htmlContent = $this->generateWeeklyReportHTML($weekStartDate, $weekEndDate, $reportData);
            
            // Create plain text version
            $textContent = $this->generateWeeklyReportText($weekStartDate, $weekEndDate, $reportData);
            
            // For now, we'll log the email content (actual email sending can be implemented later)
            $this->logEmail($adminEmail, $subject, $htmlContent, $textContent);
            
            return true;
            
        } catch (Exception $e) {
            error_log("Email service error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Generate HTML email content for weekly report
     */
    private function generateWeeklyReportHTML($weekStartDate, $weekEndDate, $reportData) {
        $html = '
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Weekly Income Report</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: #007bff; color: white; padding: 20px; text-align: center; }
        .content { padding: 20px; background: #f8f9fa; }
        .summary-table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        .summary-table th, .summary-table td { padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }
        .summary-table th { background: #e9ecef; font-weight: bold; }
        .highlight { background: #d4edda; padding: 15px; border-left: 4px solid #28a745; margin: 20px 0; }
        .footer { text-align: center; padding: 20px; color: #666; font-size: 12px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Weekly Income Report</h1>
            <p>ShaktiPure MLM System</p>
        </div>
        
        <div class="content">
            <h2>Week Period: ' . date('M d, Y', strtotime($weekStartDate)) . ' to ' . date('M d, Y', strtotime($weekEndDate)) . '</h2>
            <p><strong>Processing Date:</strong> ' . date('Y-m-d H:i:s') . '</p>
            
            <div class="highlight">
                <h3>Summary</h3>
                <table class="summary-table">
                    <tr>
                        <th>Metric</th>
                        <th>Value</th>
                    </tr>
                    <tr>
                        <td>Total users processed</td>
                        <td>' . $reportData['processed'] . '</td>
                    </tr>
                    <tr>
                        <td>Users who earned income</td>
                        <td>' . $reportData['users_with_income'] . '</td>
                    </tr>
                    <tr>
                        <td>Total income distributed</td>
                        <td>₹' . number_format($reportData['total_income'], 2) . '</td>
                    </tr>
                    <tr>
                        <td>Total capping applied</td>
                        <td>₹' . number_format($reportData['total_capping'], 2) . '</td>
                    </tr>
                </table>
            </div>
            
            <p>This report was automatically generated by the ShaktiPure MLM system. You can view detailed information in the admin panel.</p>
            
            <p><a href="' . SITE_URL . '/admin/weekly-income-details.php?week=' . $weekStartDate . '" style="background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">View Detailed Report</a></p>
        </div>
        
        <div class="footer">
            <p>This is an automated email from ShaktiPure MLM System</p>
            <p>Please do not reply to this email</p>
        </div>
    </div>
</body>
</html>';
        
        return $html;
    }
    
    /**
     * Generate plain text email content for weekly report
     */
    private function generateWeeklyReportText($weekStartDate, $weekEndDate, $reportData) {
        $text = "
Weekly Income Processing Report
==============================

Week Period: {$weekStartDate} to {$weekEndDate}
Processing Date: " . date('Y-m-d H:i:s') . "

Summary:
- Total users processed: {$reportData['processed']}
- Users who earned income: {$reportData['users_with_income']}
- Total income distributed: ₹" . number_format($reportData['total_income'], 2) . "
- Total capping applied: ₹" . number_format($reportData['total_capping'], 2) . "

This is an automated report from the ShaktiPure MLM system.
You can view detailed information in the admin panel at:
" . SITE_URL . "/admin/weekly-income-details.php?week={$weekStartDate}

Please do not reply to this email.
";
        
        return $text;
    }
    
    /**
     * Log email content (for testing/debugging)
     */
    private function logEmail($to, $subject, $htmlContent, $textContent) {
        $logEntry = "
========== EMAIL LOG ==========
To: {$to}
Subject: {$subject}
Date: " . date('Y-m-d H:i:s') . "

HTML Content:
{$htmlContent}

Text Content:
{$textContent}
===============================
";
        
        // Log to file
        $logFile = 'logs/email_log.txt';
        if (!is_dir('logs')) {
            mkdir('logs', 0755, true);
        }
        
        file_put_contents($logFile, $logEntry, FILE_APPEND | LOCK_EX);
        
        echo "Email logged to: {$logFile}\n";
    }
    
    /**
     * Send actual email using PHP mail() function
     * This method can be enhanced to use SMTP libraries like PHPMailer
     */
    public function sendActualEmail($to, $subject, $htmlContent, $textContent) {
        try {
            $headers = [
                'MIME-Version: 1.0',
                'Content-Type: text/html; charset=UTF-8',
                'From: ' . FROM_EMAIL,
                'Reply-To: ' . FROM_EMAIL,
                'X-Mailer: PHP/' . phpversion()
            ];
            
            $result = mail($to, $subject, $htmlContent, implode("\r\n", $headers));
            
            if ($result) {
                echo "Email sent successfully to: {$to}\n";
                return true;
            } else {
                echo "Failed to send email to: {$to}\n";
                return false;
            }
            
        } catch (Exception $e) {
            error_log("Send email error: " . $e->getMessage());
            return false;
        }
    }
}
?>
