<?php
/**
 * PV Transaction Model
 * MLM Binary Plan System
 */

require_once 'BaseModel.php';

class PVTransaction extends BaseModel {
    protected $table = 'pv_transactions';
    protected $primaryKey = 'id';
    protected $fillable = [
        'user_id', 'transaction_type', 'pv_amount', 'side', 'product_id', 
        'reference_id', 'description', 'created_by_type', 'created_by_id'
    ];
    
    /**
     * Get user's PV transactions
     * 
     * @param string $userId User ID
     * @param int $limit Maximum number of transactions to return
     * @param int $offset Offset for pagination
     * @return array PV transactions
     */
    public function getUserTransactions($userId, $limit = 50, $offset = 0) {
        $stmt = $this->db->prepare("
            SELECT pt.*, p.name as product_name 
            FROM {$this->table} pt 
            LEFT JOIN products p ON pt.product_id = p.id 
            WHERE pt.user_id = ? 
            ORDER BY pt.created_at DESC 
            LIMIT ? OFFSET ?
        ");
        $stmt->execute([$userId, $limit, $offset]);
        return $stmt->fetchAll();
    }
    
    /**
     * Get user's PV transactions by side
     * 
     * @param string $userId User ID
     * @param string $side Side (left, right, self, upline)
     * @param int $limit Maximum number of transactions to return
     * @param int $offset Offset for pagination
     * @return array PV transactions
     */
    public function getUserTransactionsBySide($userId, $side, $limit = 50, $offset = 0) {
        $stmt = $this->db->prepare("
            SELECT pt.*, p.name as product_name 
            FROM {$this->table} pt 
            LEFT JOIN products p ON pt.product_id = p.id 
            WHERE pt.user_id = ? AND pt.side = ? 
            ORDER BY pt.created_at DESC 
            LIMIT ? OFFSET ?
        ");
        $stmt->execute([$userId, $side, $limit, $offset]);
        return $stmt->fetchAll();
    }
    
    /**
     * Get user's PV transactions by type
     * 
     * @param string $userId User ID
     * @param string $type Transaction type (purchase, bonus, manual)
     * @param int $limit Maximum number of transactions to return
     * @param int $offset Offset for pagination
     * @return array PV transactions
     */
    public function getUserTransactionsByType($userId, $type, $limit = 50, $offset = 0) {
        $stmt = $this->db->prepare("
            SELECT pt.*, p.name as product_name 
            FROM {$this->table} pt 
            LEFT JOIN products p ON pt.product_id = p.id 
            WHERE pt.user_id = ? AND pt.transaction_type = ? 
            ORDER BY pt.created_at DESC 
            LIMIT ? OFFSET ?
        ");
        $stmt->execute([$userId, $type, $limit, $offset]);
        return $stmt->fetchAll();
    }
    
    /**
     * Get user's PV transactions by date range
     * 
     * @param string $userId User ID
     * @param string $startDate Start date (YYYY-MM-DD)
     * @param string $endDate End date (YYYY-MM-DD)
     * @param int $limit Maximum number of transactions to return
     * @param int $offset Offset for pagination
     * @return array PV transactions
     */
    public function getUserTransactionsByDateRange($userId, $startDate, $endDate, $limit = 50, $offset = 0) {
        $stmt = $this->db->prepare("
            SELECT pt.*, p.name as product_name 
            FROM {$this->table} pt 
            LEFT JOIN products p ON pt.product_id = p.id 
            WHERE pt.user_id = ? AND DATE(pt.created_at) BETWEEN ? AND ? 
            ORDER BY pt.created_at DESC 
            LIMIT ? OFFSET ?
        ");
        $stmt->execute([$userId, $startDate, $endDate, $limit, $offset]);
        return $stmt->fetchAll();
    }
    
    /**
     * Get user's PV totals
     * 
     * @param string $userId User ID
     * @return array PV totals
     */
    public function getUserPVTotals($userId) {
        $stmt = $this->db->prepare("
            SELECT
                SUM(CASE WHEN side = 'left' THEN pv_amount ELSE 0 END) as left_pv,
                SUM(CASE WHEN side = 'right' THEN pv_amount ELSE 0 END) as right_pv,
                SUM(CASE WHEN side = 'self' THEN pv_amount ELSE 0 END) as self_pv,
                SUM(CASE WHEN side = 'upline' THEN pv_amount ELSE 0 END) as upline_pv,
                SUM(pv_amount) as total_pv
            FROM {$this->table}
            WHERE user_id = ?
        ");
        $stmt->execute([$userId]);
        $result = $stmt->fetch();
        
        return [
            'left_pv' => (float) ($result['left_pv'] ?? 0),
            'right_pv' => (float) ($result['right_pv'] ?? 0),
            'self_pv' => (float) ($result['self_pv'] ?? 0),
            'upline_pv' => (float) ($result['upline_pv'] ?? 0),
            'total_pv' => (float) ($result['total_pv'] ?? 0)
        ];
    }
    
    /**
     * Get user's PV totals by date range
     * 
     * @param string $userId User ID
     * @param string $startDate Start date (YYYY-MM-DD)
     * @param string $endDate End date (YYYY-MM-DD)
     * @return array PV totals
     */
    public function getUserPVTotalsByDateRange($userId, $startDate, $endDate) {
        $stmt = $this->db->prepare("
            SELECT
                SUM(CASE WHEN side = 'left' THEN pv_amount ELSE 0 END) as left_pv,
                SUM(CASE WHEN side = 'right' THEN pv_amount ELSE 0 END) as right_pv,
                SUM(CASE WHEN side = 'self' THEN pv_amount ELSE 0 END) as self_pv,
                SUM(CASE WHEN side = 'upline' THEN pv_amount ELSE 0 END) as upline_pv,
                SUM(pv_amount) as total_pv
            FROM {$this->table}
            WHERE user_id = ? AND DATE(created_at) BETWEEN ? AND ?
        ");
        $stmt->execute([$userId, $startDate, $endDate]);
        $result = $stmt->fetch();
        
        return [
            'left_pv' => (float) ($result['left_pv'] ?? 0),
            'right_pv' => (float) ($result['right_pv'] ?? 0),
            'self_pv' => (float) ($result['self_pv'] ?? 0),
            'upline_pv' => (float) ($result['upline_pv'] ?? 0),
            'total_pv' => (float) ($result['total_pv'] ?? 0)
        ];
    }
    
    /**
     * Add PV transaction
     * 
     * @param string $userId User ID
     * @param float $pvAmount PV amount
     * @param string $side Side (left, right, self, upline)
     * @param string $transactionType Transaction type (purchase, bonus, manual)
     * @param int|null $productId Product ID
     * @param string|null $referenceId Reference ID
     * @param string $description Description
     * @param string $createdByType Created by type (admin, franchise, system)
     * @param int|null $createdById Created by ID
     * @return int|false Transaction ID or false on failure
     */
    public function addPVTransaction($userId, $pvAmount, $side, $transactionType = 'purchase', $productId = null, $referenceId = null, $description = '', $createdByType = 'system', $createdById = null) {
        return $this->create([
            'user_id' => $userId,
            'transaction_type' => $transactionType,
            'pv_amount' => $pvAmount,
            'side' => $side,
            'product_id' => $productId,
            'reference_id' => $referenceId,
            'description' => $description,
            'created_by_type' => $createdByType,
            'created_by_id' => $createdById
        ]);
    }
    
    /**
     * Get PV statistics
     * 
     * @param string|null $startDate Start date (YYYY-MM-DD)
     * @param string|null $endDate End date (YYYY-MM-DD)
     * @return array PV statistics
     */
    public function getPVStatistics($startDate = null, $endDate = null) {
        $whereClause = '';
        $params = [];
        
        if ($startDate && $endDate) {
            $whereClause = 'WHERE DATE(created_at) BETWEEN ? AND ?';
            $params = [$startDate, $endDate];
        }
        
        $stmt = $this->db->prepare("
            SELECT 
                COUNT(*) as total_transactions,
                SUM(pv_amount) as total_pv,
                SUM(CASE WHEN side = 'left' THEN pv_amount ELSE 0 END) as left_pv,
                SUM(CASE WHEN side = 'right' THEN pv_amount ELSE 0 END) as right_pv,
                SUM(CASE WHEN side = 'self' THEN pv_amount ELSE 0 END) as self_pv,
                SUM(CASE WHEN side = 'upline' THEN pv_amount ELSE 0 END) as upline_pv,
                AVG(pv_amount) as avg_pv
            FROM {$this->table} 
            $whereClause
        ");
        $stmt->execute($params);
        $result = $stmt->fetch();
        
        return [
            'total_transactions' => (int) $result['total_transactions'],
            'total_pv' => (float) ($result['total_pv'] ?? 0),
            'left_pv' => (float) ($result['left_pv'] ?? 0),
            'right_pv' => (float) ($result['right_pv'] ?? 0),
            'self_pv' => (float) ($result['self_pv'] ?? 0),
            'upline_pv' => (float) ($result['upline_pv'] ?? 0),
            'avg_pv' => (float) ($result['avg_pv'] ?? 0)
        ];
    }
    
    /**
     * Get PV transaction data for charts
     * 
     * @param int $days Number of days to get data for
     * @return array PV transaction data
     */
    public function getPVTransactionData($days = 30) {
        $stmt = $this->db->prepare("
            SELECT DATE(created_at) as date, SUM(pv_amount) as total_pv, COUNT(*) as count
            FROM {$this->table} 
            WHERE created_at >= DATE_SUB(CURDATE(), INTERVAL ? DAY)
            GROUP BY DATE(created_at)
            ORDER BY date ASC
        ");
        $stmt->execute([$days]);
        return $stmt->fetchAll();
    }
    
    /**
     * Get PV transaction data by side for charts
     * 
     * @param int $days Number of days to get data for
     * @return array PV transaction data by side
     */
    public function getPVTransactionDataBySide($days = 30) {
        $stmt = $this->db->prepare("
            SELECT 
                DATE(created_at) as date, 
                SUM(CASE WHEN side = 'left' THEN pv_amount ELSE 0 END) as left_pv,
                SUM(CASE WHEN side = 'right' THEN pv_amount ELSE 0 END) as right_pv,
                SUM(CASE WHEN side = 'self' THEN pv_amount ELSE 0 END) as self_pv,
                SUM(CASE WHEN side = 'upline' THEN pv_amount ELSE 0 END) as upline_pv
            FROM {$this->table} 
            WHERE created_at >= DATE_SUB(CURDATE(), INTERVAL ? DAY)
            GROUP BY DATE(created_at)
            ORDER BY date ASC
        ");
        $stmt->execute([$days]);
        return $stmt->fetchAll();
    }
}
?>
