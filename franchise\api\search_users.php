<?php
/**
 * User Search API for Franchise
 * MLM Binary Plan System
 */

require_once '../../includes/header.php';
require_once '../../includes/Auth.php';

// Set JSON response header
header('Content-Type: application/json');

// Require franchise authentication
if (!isLoggedIn('franchise')) {
    http_response_code(401);
    echo json_encode(['error' => 'Unauthorized']);
    exit;
}

$franchiseId = getCurrentUserId();

try {
    $db = Database::getInstance();
    
    // Get search query
    $searchQuery = $_GET['q'] ?? '';
    $searchQuery = trim($searchQuery);
    
    if (strlen($searchQuery) < 2) {
        echo json_encode(['users' => []]);
        exit;
    }
    
    // Search users within the franchise
    $sql = "
        SELECT user_id, username, full_name, email, phone, status
        FROM users 
        WHERE franchise_id = ? 
        AND status = 'active'
        AND (
            user_id LIKE ? OR
            username LIKE ? OR
            full_name LIKE ? OR
            email LIKE ?
        )
        ORDER BY full_name
        LIMIT 20
    ";
    
    $searchTerm = '%' . $searchQuery . '%';
    $stmt = $db->prepare($sql);
    $stmt->execute([
        $franchiseId,
        $searchTerm,
        $searchTerm,
        $searchTerm,
        $searchTerm
    ]);
    
    $users = $stmt->fetchAll();
    
    // Format response
    $response = [
        'users' => array_map(function($user) {
            return [
                'user_id' => $user['user_id'],
                'username' => $user['username'],
                'full_name' => $user['full_name'],
                'email' => $user['email'],
                'phone' => $user['phone'],
                'display_name' => $user['full_name'] . ' (' . $user['user_id'] . ')',
                'display_info' => $user['email'] . ' | ' . $user['phone']
            ];
        }, $users)
    ];
    
    echo json_encode($response);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => 'Search failed']);
    error_log("User search error: " . $e->getMessage());
}
?>
