<?php
/**
 * User Wallet Page
 * MLM Binary Plan System
 */

require_once '../includes/header.php';
require_once '../includes/Auth.php';
require_once '../includes/Wallet.php';

// Require user authentication
Auth::requireUser();

$currentUser = Auth::user();
$userId = Auth::id();

// Initialize wallet
$wallet = new Wallet();

$message = '';
$error = '';

// Handle withdrawal request
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'request_withdrawal') {
    verifyCsrfToken();
    
    $amount = (float) $_POST['amount'];
    $bankDetails = [
        'bank_name' => sanitizeInput($_POST['bank_name']),
        'account_number' => sanitizeInput($_POST['account_number']),
        'ifsc_code' => sanitizeInput($_POST['ifsc_code']),
        'account_holder' => sanitizeInput($_POST['account_holder'])
    ];
    
    try {
        if ($wallet->requestWithdrawal($userId, $amount, $bankDetails)) {
            setSuccessMessage('Withdrawal request submitted successfully. It will be processed within 24-48 hours.');
            Response::redirect('wallet.php');
        }
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

// Get wallet data
$walletData = $wallet->getWallet($userId);

// Get transaction history
$page = (int) ($_GET['page'] ?? 1);
$limit = 20;
$offset = ($page - 1) * $limit;
$transactions = $wallet->getTransactionHistory($userId, $limit, $offset);

// Get withdrawal history
$withdrawals = $wallet->getWithdrawalHistory($userId, 10);

// Check for pending withdrawals
$db = Database::getInstance();
$pendingStmt = $db->prepare("SELECT COUNT(*) FROM withdrawals WHERE user_id = ? AND status = 'pending'");
$pendingStmt->execute([$userId]);
$hasPendingWithdrawal = $pendingStmt->fetchColumn() > 0;
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Wallet - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="dashboard.php">
                <i class="fas fa-network-wired me-2"></i><?php echo SITE_NAME; ?>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">
                            <i class="fas fa-tachometer-alt me-1"></i>Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="tree.php">
                            <i class="fas fa-sitemap me-1"></i>Binary Tree
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="wallet.php">
                            <i class="fas fa-wallet me-1"></i>Wallet
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="products.php">
                            <i class="fas fa-shopping-cart me-1"></i>Products
                        </a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-1"></i><?php echo htmlspecialchars($currentUser['full_name']); ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="profile.php"><i class="fas fa-user-edit me-2"></i>Profile</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../logout.php"><i class="fas fa-sign-out-alt me-2"></i>Logout</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Flash Messages -->
        <?php displayFlashMessages(); ?>
        
        <?php if ($error): ?>
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle me-2"></i><?php echo htmlspecialchars($error); ?>
            </div>
        <?php endif; ?>

        <!-- Wallet Overview -->
        <div class="row mb-4">
            <div class="col-md-4">
                <div class="wallet-card">
                    <div class="text-center">
                        <i class="fas fa-wallet fa-3x mb-3"></i>
                        <div class="wallet-balance"><?php echo formatCurrency($walletData['balance']); ?></div>
                        <p class="mb-0">Available Balance</p>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="stats-card">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon success me-3">
                            <i class="fas fa-arrow-up"></i>
                        </div>
                        <div>
                            <h4 class="mb-0"><?php echo formatCurrency($walletData['total_earned']); ?></h4>
                            <small class="text-muted">Total Earned</small>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="stats-card">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon warning me-3">
                            <i class="fas fa-arrow-down"></i>
                        </div>
                        <div>
                            <h4 class="mb-0"><?php echo formatCurrency($walletData['total_withdrawn']); ?></h4>
                            <small class="text-muted">Total Withdrawn</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Withdrawal Request -->
            <div class="col-md-6 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-money-bill-wave me-2"></i>Request Withdrawal</h5>
                    </div>
                    <div class="card-body">
                        <?php if ($hasPendingWithdrawal): ?>
                            <div class="alert alert-warning">
                                <i class="fas fa-clock me-2"></i>You have a pending withdrawal request. Please wait for it to be processed.
                            </div>
                        <?php else: ?>
                            <form method="POST">
                                <?php echo csrfTokenInput(); ?>
                                <input type="hidden" name="action" value="request_withdrawal">
                                
                                <div class="mb-3">
                                    <label for="amount" class="form-label">Withdrawal Amount</label>
                                    <div class="input-group">
                                        <span class="input-group-text">₹</span>
                                        <input type="number" class="form-control" id="amount" name="amount" 
                                               min="<?php echo APP_MIN_WITHDRAWAL; ?>" 
                                               max="<?php echo $walletData['balance']; ?>" 
                                               step="0.01" required>
                                    </div>
                                    <small class="text-muted">
                                        Minimum: <?php echo formatCurrency(APP_MIN_WITHDRAWAL); ?> | 
                                        Available: <?php echo formatCurrency($walletData['balance']); ?>
                                    </small>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="bank_name" class="form-label">Bank Name</label>
                                    <input type="text" class="form-control" id="bank_name" name="bank_name" required>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="account_holder" class="form-label">Account Holder Name</label>
                                    <input type="text" class="form-control" id="account_holder" name="account_holder" 
                                           value="<?php echo htmlspecialchars($currentUser['full_name']); ?>" required>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="account_number" class="form-label">Account Number</label>
                                    <input type="text" class="form-control" id="account_number" name="account_number" required>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="ifsc_code" class="form-label">IFSC Code</label>
                                    <input type="text" class="form-control" id="ifsc_code" name="ifsc_code" 
                                           placeholder="e.g., SBIN0001234" required>
                                </div>
                                
                                <button type="submit" class="btn btn-success w-100">
                                    <i class="fas fa-paper-plane me-2"></i>Submit Withdrawal Request
                                </button>
                            </form>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            
            <!-- Withdrawal History -->
            <div class="col-md-6 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-history me-2"></i>Withdrawal History</h5>
                    </div>
                    <div class="card-body">
                        <?php if (!empty($withdrawals)): ?>
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>Date</th>
                                            <th>Amount</th>
                                            <th>Status</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($withdrawals as $withdrawal): ?>
                                            <tr>
                                                <td><?php echo date('M d, Y', strtotime($withdrawal['requested_at'])); ?></td>
                                                <td><?php echo formatCurrency($withdrawal['amount']); ?></td>
                                                <td>
                                                    <?php
                                                    $statusClass = '';
                                                    switch ($withdrawal['status']) {
                                                        case 'pending':
                                                            $statusClass = 'warning';
                                                            break;
                                                        case 'approved':
                                                        case 'processed':
                                                            $statusClass = 'success';
                                                            break;
                                                        case 'rejected':
                                                            $statusClass = 'danger';
                                                            break;
                                                    }
                                                    ?>
                                                    <span class="badge bg-<?php echo $statusClass; ?>">
                                                        <?php echo ucfirst($withdrawal['status']); ?>
                                                    </span>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php else: ?>
                            <p class="text-muted text-center">No withdrawal requests yet</p>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Transaction History -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-exchange-alt me-2"></i>Transaction History</h5>
                    </div>
                    <div class="card-body">
                        <?php if (!empty($transactions)): ?>
                            <div class="table-responsive">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>Date</th>
                                            <th>Type</th>
                                            <th>Amount</th>
                                            <th>Description</th>
                                            <th>Balance</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($transactions as $transaction): ?>
                                            <tr>
                                                <td><?php echo date('M d, Y H:i', strtotime($transaction['created_at'])); ?></td>
                                                <td>
                                                    <span class="badge bg-<?php echo $transaction['transaction_type'] === 'credit' ? 'success' : 'danger'; ?>">
                                                        <i class="fas fa-arrow-<?php echo $transaction['transaction_type'] === 'credit' ? 'up' : 'down'; ?> me-1"></i>
                                                        <?php echo ucfirst($transaction['transaction_type']); ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <span class="<?php echo $transaction['transaction_type'] === 'credit' ? 'text-success' : 'text-danger'; ?>">
                                                        <?php echo $transaction['transaction_type'] === 'credit' ? '+' : '-'; ?>
                                                        <?php echo formatCurrency($transaction['amount']); ?>
                                                    </span>
                                                </td>
                                                <td><?php echo htmlspecialchars($transaction['description']); ?></td>
                                                <td><?php echo formatCurrency($transaction['balance_after']); ?></td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                            
                            <!-- Pagination -->
                            <?php if (count($transactions) === $limit): ?>
                                <div class="text-center">
                                    <a href="?page=<?php echo $page + 1; ?>" class="btn btn-outline-primary">
                                        <i class="fas fa-chevron-right me-2"></i>Load More
                                    </a>
                                </div>
                            <?php endif; ?>
                        <?php else: ?>
                            <div class="text-center text-muted py-4">
                                <i class="fas fa-exchange-alt fa-3x mb-3"></i>
                                <h5>No transactions yet</h5>
                                <p>Your wallet transactions will appear here</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
