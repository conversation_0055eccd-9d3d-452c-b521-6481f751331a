<?php
/**
 * Daily PV Matching Cron Job
 * MLM Binary Plan System
 * 
 * This script should be run daily via cron job to process PV matching
 * Example cron entry: 0 0 * * * /usr/bin/php /path/to/daily-matching.php
 */

// Prevent direct web access
if (php_sapi_name() !== 'cli') {
    die('This script can only be run from command line');
}

require_once dirname(__DIR__) . '/includes/header.php';
require_once dirname(__DIR__) . '/includes/PVSystem.php';
require_once dirname(__DIR__) . '/includes/BinaryTree.php';

echo "Starting daily PV matching process...\n";
echo "Date: " . date('Y-m-d H:i:s') . "\n";
echo str_repeat('-', 50) . "\n";

try {
    $pvSystem = new PVSystem();
    
    // Run daily matching for all users
    $processed = $pvSystem->runDailyMatching();
    
    if ($processed !== false) {
        echo "Successfully processed PV matching for {$processed} users\n";
        
        // Log the successful run
        $db = Database::getInstance();
        $logStmt = $db->prepare("INSERT INTO system_logs (log_type, message, created_at) VALUES (?, ?, NOW())");
        $logStmt->execute(['cron', "Daily PV matching completed. Processed {$processed} users."]);
        
    } else {
        echo "Error occurred during PV matching process\n";
        
        // Log the error
        $db = Database::getInstance();
        $logStmt = $db->prepare("INSERT INTO system_logs (log_type, message, created_at) VALUES (?, ?, NOW())");
        $logStmt->execute(['error', "Daily PV matching failed."]);
    }
    
} catch (Exception $e) {
    echo "Fatal error: " . $e->getMessage() . "\n";
    
    // Log the fatal error
    try {
        $db = Database::getInstance();
        $logStmt = $db->prepare("INSERT INTO system_logs (log_type, message, created_at) VALUES (?, ?, NOW())");
        $logStmt->execute(['error', "Daily PV matching fatal error: " . $e->getMessage()]);
    } catch (Exception $logError) {
        // If we can't log to database, log to file
        error_log("Daily matching error: " . $e->getMessage());
    }
}

echo str_repeat('-', 50) . "\n";
echo "Daily PV matching process completed\n";
echo "End time: " . date('Y-m-d H:i:s') . "\n";
?>
