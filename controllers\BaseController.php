<?php
/**
 * Base Controller Class
 * MLM Binary Plan System
 * 
 * This abstract class provides common functionality for all controllers
 */

abstract class BaseController {
    protected $request;
    protected $response;
    protected $session;
    protected $validator;
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->request = $_REQUEST;
        $this->response = new Response();
        $this->session = $_SESSION ?? [];
        $this->validator = new Validator();
        
        // Initialize session if not started
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
    }
    
    /**
     * Get request data
     * 
     * @param string $key Request key
     * @param mixed $default Default value if key not found
     * @return mixed Request value
     */
    protected function getRequest($key = null, $default = null) {
        if ($key === null) {
            return $this->request;
        }
        
        return $this->request[$key] ?? $default;
    }
    
    /**
     * Get POST data
     * 
     * @param string $key POST key
     * @param mixed $default Default value if key not found
     * @return mixed POST value
     */
    protected function getPost($key = null, $default = null) {
        if ($key === null) {
            return $_POST;
        }
        
        return $_POST[$key] ?? $default;
    }
    
    /**
     * Get GET data
     * 
     * @param string $key GET key
     * @param mixed $default Default value if key not found
     * @return mixed GET value
     */
    protected function getGet($key = null, $default = null) {
        if ($key === null) {
            return $_GET;
        }
        
        return $_GET[$key] ?? $default;
    }
    
    /**
     * Check if request is POST
     * 
     * @return bool True if POST request
     */
    protected function isPost() {
        return $_SERVER['REQUEST_METHOD'] === 'POST';
    }
    
    /**
     * Check if request is GET
     * 
     * @return bool True if GET request
     */
    protected function isGet() {
        return $_SERVER['REQUEST_METHOD'] === 'GET';
    }
    
    /**
     * Check if request is AJAX
     * 
     * @return bool True if AJAX request
     */
    protected function isAjax() {
        return !empty($_SERVER['HTTP_X_REQUESTED_WITH']) && 
               strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';
    }
    
    /**
     * Validate request data
     * 
     * @param array $data Data to validate
     * @param array $rules Validation rules
     * @return array Validation result
     */
    protected function validate(array $data, array $rules) {
        return $this->validator->validate($data, $rules);
    }
    
    /**
     * Set session data
     * 
     * @param string $key Session key
     * @param mixed $value Session value
     */
    protected function setSession($key, $value) {
        $_SESSION[$key] = $value;
    }
    
    /**
     * Get session data
     * 
     * @param string $key Session key
     * @param mixed $default Default value if key not found
     * @return mixed Session value
     */
    protected function getSession($key = null, $default = null) {
        if ($key === null) {
            return $_SESSION;
        }
        
        return $_SESSION[$key] ?? $default;
    }
    
    /**
     * Remove session data
     * 
     * @param string $key Session key
     */
    protected function removeSession($key) {
        unset($_SESSION[$key]);
    }
    
    /**
     * Set flash message
     * 
     * @param string $type Message type (success, error, warning, info)
     * @param string $message Message text
     */
    protected function setFlash($type, $message) {
        $_SESSION['flash'][$type] = $message;
    }
    
    /**
     * Get flash message
     * 
     * @param string $type Message type
     * @return string|null Flash message
     */
    protected function getFlash($type) {
        $message = $_SESSION['flash'][$type] ?? null;
        unset($_SESSION['flash'][$type]);
        return $message;
    }
    
    /**
     * Get all flash messages
     * 
     * @return array Flash messages
     */
    protected function getAllFlash() {
        $messages = $_SESSION['flash'] ?? [];
        unset($_SESSION['flash']);
        return $messages;
    }
    
    /**
     * Redirect to a URL
     * 
     * @param string $url URL to redirect to
     * @param int $statusCode HTTP status code
     */
    protected function redirect($url, $statusCode = 302) {
        Response::redirect($url, $statusCode);
    }

    /**
     * Redirect with success message
     *
     * @param string $url URL to redirect to
     * @param string $message Success message
     */
    protected function redirectWithSuccess($url, $message) {
        Response::redirectWithSuccess($url, $message);
    }

    /**
     * Redirect with error message
     *
     * @param string $url URL to redirect to
     * @param string $message Error message
     */
    protected function redirectWithError($url, $message) {
        Response::redirectWithError($url, $message);
    }

    /**
     * Redirect back with message
     *
     * @param string $message Message
     * @param string $type Message type (success, error, info, warning)
     */
    protected function redirectBack($message = null, $type = 'info') {
        Response::redirectBack($message, $type);
    }
    
    /**
     * Return JSON response
     * 
     * @param array $data Response data
     * @param int $statusCode HTTP status code
     */
    protected function json(array $data, $statusCode = 200) {
        Response::json($data, $statusCode);
    }

    /**
     * Return success JSON response
     *
     * @param array $data Response data
     * @param string $message Success message
     */
    protected function jsonSuccess($message = 'Success', $data = []) {
        Response::success($message, $data);
    }

    /**
     * Return error JSON response
     *
     * @param string $message Error message
     * @param array $errors Error details
     * @param int $statusCode HTTP status code
     */
    protected function jsonError($message = 'Error', $errors = [], $statusCode = 400) {
        Response::error($message, $errors, $statusCode);
    }
    
    /**
     * Render a view
     * 
     * @param string $view View file name
     * @param array $data Data to pass to view
     * @param string $layout Layout file name
     */
    protected function render($view, array $data = [], $layout = null) {
        // Extract data variables
        extract($data);
        
        // Start output buffering
        ob_start();
        
        // Include view file
        $viewFile = "views/{$view}.php";
        if (file_exists($viewFile)) {
            include $viewFile;
        } else {
            throw new Exception("View file not found: {$viewFile}");
        }
        
        // Get view content
        $content = ob_get_clean();
        
        // If layout is specified, render with layout
        if ($layout) {
            $layoutFile = "views/layouts/{$layout}.php";
            if (file_exists($layoutFile)) {
                include $layoutFile;
            } else {
                throw new Exception("Layout file not found: {$layoutFile}");
            }
        } else {
            echo $content;
        }
    }
    
    /**
     * Check if user is authenticated
     * 
     * @return bool True if authenticated
     */
    protected function isAuthenticated() {
        return !empty($_SESSION['user_id']) && !empty($_SESSION['user_type']);
    }
    
    /**
     * Get current user ID
     * 
     * @return string|null User ID
     */
    protected function getCurrentUserId() {
        return $_SESSION['user_id'] ?? null;
    }
    
    /**
     * Get current user type
     * 
     * @return string|null User type (admin, franchise, user)
     */
    protected function getCurrentUserType() {
        return $_SESSION['user_type'] ?? null;
    }
    
    /**
     * Require authentication
     * 
     * @param string $userType Required user type (optional)
     */
    protected function requireAuth($userType = null) {
        if (!$this->isAuthenticated()) {
            if ($this->isAjax()) {
                $this->jsonError('Authentication required', [], 401);
            } else {
                $this->redirect('/login.php');
            }
        }
        
        if ($userType && $this->getCurrentUserType() !== $userType) {
            if ($this->isAjax()) {
                $this->jsonError('Access denied', [], 403);
            } else {
                $this->redirect('/index.php');
            }
        }
    }
    
    /**
     * Log an error
     * 
     * @param string $message Error message
     * @param array $context Error context
     */
    protected function logError($message, array $context = []) {
        error_log("Controller Error: {$message} " . json_encode($context));
    }
}
?>
