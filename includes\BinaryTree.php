<?php
/**
 * Binary Tree Management Class
 * MLM Binary Plan System
 */

class BinaryTree {
    private $db;
    
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    /**
     * Add user to binary tree
     */
    public function addUser($userId, $sponsorId, $placementSide, $useTransaction = true) {
        try {
            if ($useTransaction) {
                $this->db->beginTransaction();
            }

            // Check if sponsor exists in tree
            $sponsor = $this->getNode($sponsorId);
            if (!$sponsor) {
                // Add sponsor as root if not exists
                $this->addRoot($sponsorId, false); // Don't use transaction for root creation
                $sponsor = $this->getNode($sponsorId);
            }

            // Find the best available position
            $parentId = $this->findBestPosition($sponsorId, $placementSide);
            $parent = $this->getNode($parentId);

            // Calculate level
            $level = $parent['level'] + 1;

            // Determine actual placement side
            $actualSide = $this->getAvailableSide($parentId, $placementSide);

            // Insert into binary tree
            $stmt = $this->db->prepare("INSERT INTO binary_tree (user_id, parent_id, level, position) VALUES (?, ?, ?, ?)");
            $stmt->execute([$userId, $parentId, $level, $actualSide]);

            // Update parent's child reference
            $childField = $actualSide === 'left' ? 'left_child' : 'right_child';
            $updateStmt = $this->db->prepare("UPDATE binary_tree SET {$childField} = ? WHERE user_id = ?");
            $updateStmt->execute([$userId, $parentId]);

            if ($useTransaction) {
                $this->db->commit();
            }
            return true;

        } catch (Exception $e) {
            if ($useTransaction && $this->db->inTransaction()) {
                $this->db->rollback();
            }
            error_log("Binary tree add user error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Add root user to tree
     */
    public function addRoot($userId, $useTransaction = true) {
        try {
            if ($useTransaction) {
                $this->db->beginTransaction();
            }

            $stmt = $this->db->prepare("INSERT INTO binary_tree (user_id, parent_id, level, position) VALUES (?, NULL, 0, 'root')");
            $result = $stmt->execute([$userId]);

            if ($useTransaction) {
                $this->db->commit();
            }

            return $result;
        } catch (Exception $e) {
            if ($useTransaction && $this->db->inTransaction()) {
                $this->db->rollback();
            }
            error_log("Binary tree add root error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get node information
     */
    public function getNode($userId) {
        $stmt = $this->db->prepare("SELECT * FROM binary_tree WHERE user_id = ?");
        $stmt->execute([$userId]);
        return $stmt->fetch();
    }
    
    /**
     * Find best position for new user
     */
    public function findBestPosition($sponsorId, $preferredSide) {
        // Start with sponsor
        $queue = [$sponsorId];
        $visited = [];
        
        while (!empty($queue)) {
            $currentUser = array_shift($queue);
            
            if (in_array($currentUser, $visited)) {
                continue;
            }
            $visited[] = $currentUser;
            
            // Check if current user has space
            $availableSide = $this->getAvailableSide($currentUser, $preferredSide);
            if ($availableSide) {
                return $currentUser;
            }
            
            // Add children to queue for breadth-first search
            $node = $this->getNode($currentUser);
            if ($node) {
                if ($node['left_child']) {
                    $queue[] = $node['left_child'];
                }
                if ($node['right_child']) {
                    $queue[] = $node['right_child'];
                }
            }
        }
        
        return $sponsorId; // Fallback to sponsor
    }
    
    /**
     * Get available side for placement
     */
    public function getAvailableSide($parentId, $preferredSide) {
        $node = $this->getNode($parentId);
        if (!$node) {
            return null;
        }
        
        // Check preferred side first
        if ($preferredSide === 'left' && !$node['left_child']) {
            return 'left';
        } elseif ($preferredSide === 'right' && !$node['right_child']) {
            return 'right';
        }
        
        // Check other side
        if ($preferredSide === 'left' && !$node['right_child']) {
            return 'right';
        } elseif ($preferredSide === 'right' && !$node['left_child']) {
            return 'left';
        }
        
        return null; // No space available
    }
    
    /**
     * Get user's downline (all users under them)
     */
    public function getDownline($userId, $levels = null) {
        $downline = [];
        $this->buildDownline($userId, $downline, 0, $levels);
        return $downline;
    }
    
    /**
     * Recursively build downline
     */
    private function buildDownline($userId, &$downline, $currentLevel, $maxLevels) {
        if ($maxLevels !== null && $currentLevel >= $maxLevels) {
            return;
        }
        
        $node = $this->getNode($userId);
        if (!$node) {
            return;
        }
        
        // Add current user to downline (except root call)
        if ($currentLevel > 0) {
            $downline[] = $userId;
        }
        
        // Recursively add children
        if ($node['left_child']) {
            $this->buildDownline($node['left_child'], $downline, $currentLevel + 1, $maxLevels);
        }
        if ($node['right_child']) {
            $this->buildDownline($node['right_child'], $downline, $currentLevel + 1, $maxLevels);
        }
    }
    
    /**
     * Get tree structure for visualization
     */
    public function getTreeStructure($rootUserId, $levels = 5) {
        $tree = [];
        $this->buildTreeStructure($rootUserId, $tree, 0, $levels);
        return $tree;
    }
    
    /**
     * Recursively build tree structure
     */
    private function buildTreeStructure($userId, &$tree, $currentLevel, $maxLevels) {
        if ($currentLevel >= $maxLevels) {
            return;
        }
        
        // Get user info
        $userStmt = $this->db->prepare("SELECT user_id, full_name, status FROM users WHERE user_id = ?");
        $userStmt->execute([$userId]);
        $user = $userStmt->fetch();
        
        if (!$user) {
            return;
        }
        
        $node = $this->getNode($userId);
        if (!$node) {
            return;
        }
        
        $treeNode = [
            'user_id' => $userId,
            'full_name' => $user['full_name'],
            'status' => $user['status'],
            'level' => $currentLevel,
            'position' => $node['position'],
            'left' => null,
            'right' => null
        ];
        
        // Recursively build children
        if ($node['left_child']) {
            $this->buildTreeStructure($node['left_child'], $treeNode['left'], $currentLevel + 1, $maxLevels);
        }

        if ($node['right_child']) {
            $this->buildTreeStructure($node['right_child'], $treeNode['right'], $currentLevel + 1, $maxLevels);
        }

        $tree = $treeNode;
    }
    
    /**
     * Get left leg users
     */
    public function getLeftLeg($userId) {
        $node = $this->getNode($userId);
        if (!$node || !$node['left_child']) {
            return [];
        }
        
        return $this->getDownline($node['left_child']);
    }
    
    /**
     * Get right leg users
     */
    public function getRightLeg($userId) {
        $node = $this->getNode($userId);
        if (!$node || !$node['right_child']) {
            return [];
        }
        
        return $this->getDownline($node['right_child']);
    }
    
    /**
     * Get direct children
     */
    public function getDirectChildren($userId) {
        $node = $this->getNode($userId);
        if (!$node) {
            return [];
        }
        
        $children = [];
        if ($node['left_child']) {
            $children['left'] = $node['left_child'];
        }
        if ($node['right_child']) {
            $children['right'] = $node['right_child'];
        }
        
        return $children;
    }
    
    /**
     * Get upline (all sponsors above user)
     */
    public function getUpline($userId) {
        $upline = [];
        $current = $this->getNode($userId);
        
        while ($current && $current['parent_id']) {
            $upline[] = $current['parent_id'];
            $current = $this->getNode($current['parent_id']);
        }
        
        return $upline;
    }
    
    /**
     * Check if user exists in another user's downline
     */
    public function isInDownline($sponsorId, $userId) {
        $downline = $this->getDownline($sponsorId);
        return in_array($userId, $downline);
    }
    
    /**
     * Get tree statistics
     */
    public function getTreeStats($userId) {
        $leftLeg = $this->getLeftLeg($userId);
        $rightLeg = $this->getRightLeg($userId);
        
        return [
            'total_downline' => count($this->getDownline($userId)),
            'left_leg_count' => count($leftLeg),
            'right_leg_count' => count($rightLeg),
            'direct_children' => count($this->getDirectChildren($userId))
        ];
    }
    
    /**
     * Move user to different position (admin function)
     */
    public function moveUser($userId, $newParentId, $newSide) {
        try {
            $this->db->beginTransaction();
            
            // Get current node
            $currentNode = $this->getNode($userId);
            if (!$currentNode) {
                throw new Exception("User not found in tree");
            }
            
            // Remove from current position
            $this->removeFromCurrentPosition($userId);
            
            // Add to new position
            $this->addToNewPosition($userId, $newParentId, $newSide);
            
            $this->db->commit();
            return true;
            
        } catch (Exception $e) {
            $this->db->rollback();
            error_log("Move user error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Remove user from current position
     */
    private function removeFromCurrentPosition($userId) {
        $node = $this->getNode($userId);
        if ($node['parent_id']) {
            $parent = $this->getNode($node['parent_id']);
            
            // Update parent's child reference
            if ($parent['left_child'] === $userId) {
                $updateStmt = $this->db->prepare("UPDATE binary_tree SET left_child = NULL WHERE user_id = ?");
                $updateStmt->execute([$node['parent_id']]);
            } elseif ($parent['right_child'] === $userId) {
                $updateStmt = $this->db->prepare("UPDATE binary_tree SET right_child = NULL WHERE user_id = ?");
                $updateStmt->execute([$node['parent_id']]);
            }
        }
    }
    
    /**
     * Add user to new position
     */
    private function addToNewPosition($userId, $newParentId, $newSide) {
        $parent = $this->getNode($newParentId);
        $newLevel = $parent['level'] + 1;
        
        // Update user's tree record
        $updateStmt = $this->db->prepare("UPDATE binary_tree SET parent_id = ?, level = ?, position = ? WHERE user_id = ?");
        $updateStmt->execute([$newParentId, $newLevel, $newSide, $userId]);
        
        // Update parent's child reference
        $childField = $newSide === 'left' ? 'left_child' : 'right_child';
        $parentUpdateStmt = $this->db->prepare("UPDATE binary_tree SET {$childField} = ? WHERE user_id = ?");
        $parentUpdateStmt->execute([$userId, $newParentId]);
    }
}
?>
