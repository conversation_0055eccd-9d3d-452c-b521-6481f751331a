<?php
/**
 * User Checkout Page
 * MLM Binary Plan System
 */

require_once '../includes/header.php';
require_once '../includes/Auth.php';
require_once '../includes/Cart.php';

// Require user authentication
Auth::requireUser();

$currentUser = Auth::user();
$userId = Auth::id();

$cart = new Cart();

// Redirect if cart is empty
if ($cart->isEmpty()) {
    header("Location: cart.php");
    exit();
}

$cartSummary = $cart->getSummary();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Checkout - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="dashboard.php">
                <i class="fas fa-credit-card me-2"></i><?php echo SITE_NAME; ?>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">
                            <i class="fas fa-tachometer-alt me-1"></i>Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="cart.php">
                            <i class="fas fa-shopping-cart me-1"></i>Cart
                        </a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-1"></i><?php echo htmlspecialchars($currentUser['full_name']); ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="profile.php">Profile</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../logout.php">Logout</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h4 class="mb-0">
                            <i class="fas fa-credit-card me-2"></i>Checkout
                        </h4>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-8">
                <!-- Checkout Notice -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Purchase Information</h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>Multiple Product Purchase</strong><br>
                            You have multiple items in your cart. Each product will be processed as a separate purchase order through our existing purchase system.
                        </div>
                        
                        <h6>Items to Purchase:</h6>
                        <div class="list-group">
                            <?php foreach ($cartSummary['items'] as $item): ?>
                                <div class="list-group-item">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <h6 class="mb-1"><?php echo htmlspecialchars($item['name']); ?></h6>
                                            <small class="text-muted">Quantity: <?php echo $item['quantity']; ?></small>
                                        </div>
                                        <div class="text-end">
                                            <strong>₹<?php echo number_format($item['price'] * $item['quantity'], 2); ?></strong><br>
                                            <small class="text-primary"><?php echo formatPV($item['pv_value'] * $item['quantity']); ?> PV</small>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <!-- Order Summary -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Order Summary</h5>
                    </div>
                    <div class="card-body">
                        <div class="d-flex justify-content-between mb-2">
                            <span>Total Items:</span>
                            <span><?php echo $cartSummary['item_count']; ?></span>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span>Total Amount:</span>
                            <span>₹<?php echo number_format($cartSummary['total_amount'], 2); ?></span>
                        </div>
                        <div class="d-flex justify-content-between mb-3">
                            <span>Total PV:</span>
                            <span class="text-primary"><?php echo formatPV($cartSummary['total_pv']); ?> PV</span>
                        </div>
                        <hr>
                        <div class="d-flex justify-content-between mb-3">
                            <strong>Final Total:</strong>
                            <strong>₹<?php echo number_format($cartSummary['total_amount'], 2); ?></strong>
                        </div>
                        
                        <div class="d-grid gap-2">
                            <button class="btn btn-success btn-lg" onclick="proceedWithPurchase()">
                                <i class="fas fa-credit-card me-2"></i>Proceed to Payment
                            </button>
                            <a href="cart.php" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left me-2"></i>Back to Cart
                            </a>
                        </div>
                        
                        <div class="mt-3">
                            <small class="text-muted">
                                <i class="fas fa-shield-alt me-1"></i>
                                Secure payment powered by Razorpay
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        function proceedWithPurchase() {
            // For now, redirect to the products page with cart items
            // In a full implementation, this would process all cart items
            alert('Redirecting to purchase the first item in your cart. Multiple item checkout will be implemented in the next phase.');
            
            // Get first item from cart and redirect to products page
            <?php 
            $firstItem = reset($cartSummary['items']);
            if ($firstItem): 
            ?>
                window.location.href = 'products.php?product_id=<?php echo $firstItem['product_id']; ?>&quantity=<?php echo $firstItem['quantity']; ?>';
            <?php endif; ?>
        }
    </script>
</body>
</html>
