<?php
/**
 * Daily Maintenance Cron Job
 * MLM Binary Plan System
 * 
 * This script performs daily maintenance tasks like log cleanup,
 * cache clearing, and system optimization
 */

// Prevent direct browser access
if (php_sapi_name() !== 'cli') {
    die('This script can only be run from command line');
}

// Set time limit and memory limit
set_time_limit(0);
ini_set('memory_limit', '256M');

// Include required files
require_once dirname(__DIR__) . '/config/database.php';
require_once dirname(__DIR__) . '/config/Connection.php';
require_once dirname(__DIR__) . '/includes/Logger.php';

// Initialize logger
$logger = Logger::getInstance();
$logger->logCron('daily-maintenance', 'started', ['memory_usage' => memory_get_usage()]);

echo str_repeat('=', 60) . "\n";
echo "Daily Maintenance Process\n";
echo "Start time: " . date('Y-m-d H:i:s') . "\n";
echo str_repeat('=', 60) . "\n";

$maintenanceTasks = [];

try {
    $db = Database::getInstance();
    
    // Task 1: Clean old log files
    echo "1. Cleaning old log files...\n";
    try {
        $logger->cleanOldLogs(30); // Keep logs for 30 days
        $maintenanceTasks['log_cleanup'] = 'success';
        echo "   ✅ Log files cleaned successfully\n";
    } catch (Exception $e) {
        $maintenanceTasks['log_cleanup'] = 'failed: ' . $e->getMessage();
        echo "   ❌ Log cleanup failed: " . $e->getMessage() . "\n";
        $logger->error("Log cleanup failed", ['exception' => $e->getMessage()], 'maintenance');
    }
    
    // Task 2: Clean old login logs
    echo "2. Cleaning old login logs...\n";
    try {
        $stmt = $db->prepare("DELETE FROM login_logs WHERE login_time < DATE_SUB(NOW(), INTERVAL 90 DAY)");
        $stmt->execute();
        $deletedRows = $stmt->rowCount();
        
        $maintenanceTasks['login_logs_cleanup'] = "success: deleted {$deletedRows} records";
        echo "   ✅ Deleted {$deletedRows} old login log records\n";
        $logger->info("Login logs cleanup completed", ['deleted_records' => $deletedRows], 'maintenance');
    } catch (Exception $e) {
        $maintenanceTasks['login_logs_cleanup'] = 'failed: ' . $e->getMessage();
        echo "   ❌ Login logs cleanup failed: " . $e->getMessage() . "\n";
        $logger->error("Login logs cleanup failed", ['exception' => $e->getMessage()], 'maintenance');
    }
    
    // Task 3: Clean old system logs
    echo "3. Cleaning old system logs...\n";
    try {
        $stmt = $db->prepare("DELETE FROM system_logs WHERE created_at < DATE_SUB(NOW(), INTERVAL 60 DAY)");
        $stmt->execute();
        $deletedRows = $stmt->rowCount();
        
        $maintenanceTasks['system_logs_cleanup'] = "success: deleted {$deletedRows} records";
        echo "   ✅ Deleted {$deletedRows} old system log records\n";
        $logger->info("System logs cleanup completed", ['deleted_records' => $deletedRows], 'maintenance');
    } catch (Exception $e) {
        $maintenanceTasks['system_logs_cleanup'] = 'failed: ' . $e->getMessage();
        echo "   ❌ System logs cleanup failed: " . $e->getMessage() . "\n";
        $logger->error("System logs cleanup failed", ['exception' => $e->getMessage()], 'maintenance');
    }
    
    // Task 4: Optimize database tables
    echo "4. Optimizing database tables...\n";
    try {
        $tables = [
            'users', 'binary_tree', 'pv_transactions', 'wallet', 'wallet_transactions',
            'withdrawals', 'income_logs', 'login_logs', 'purchase_orders', 'products',
            'franchise', 'admin', 'system_logs', 'weekly_income_logs', 'weekly_income_reports'
        ];
        
        $optimizedTables = 0;
        foreach ($tables as $table) {
            try {
                $stmt = $db->prepare("OPTIMIZE TABLE {$table}");
                $stmt->execute();
                $optimizedTables++;
            } catch (Exception $e) {
                echo "   ⚠️ Warning: Could not optimize table {$table}: " . $e->getMessage() . "\n";
            }
        }
        
        $maintenanceTasks['database_optimization'] = "success: optimized {$optimizedTables} tables";
        echo "   ✅ Optimized {$optimizedTables} database tables\n";
        $logger->info("Database optimization completed", ['optimized_tables' => $optimizedTables], 'maintenance');
    } catch (Exception $e) {
        $maintenanceTasks['database_optimization'] = 'failed: ' . $e->getMessage();
        echo "   ❌ Database optimization failed: " . $e->getMessage() . "\n";
        $logger->error("Database optimization failed", ['exception' => $e->getMessage()], 'maintenance');
    }
    
    // Task 5: Update database statistics
    echo "5. Updating database statistics...\n";
    try {
        $stmt = $db->prepare("ANALYZE TABLE users, pv_transactions, wallet_transactions, binary_tree");
        $stmt->execute();
        
        $maintenanceTasks['database_statistics'] = 'success';
        echo "   ✅ Database statistics updated\n";
        $logger->info("Database statistics updated", [], 'maintenance');
    } catch (Exception $e) {
        $maintenanceTasks['database_statistics'] = 'failed: ' . $e->getMessage();
        echo "   ❌ Database statistics update failed: " . $e->getMessage() . "\n";
        $logger->error("Database statistics update failed", ['exception' => $e->getMessage()], 'maintenance');
    }
    
    // Task 6: Clean temporary files
    echo "6. Cleaning temporary files...\n";
    try {
        $tempDir = sys_get_temp_dir();
        $cleaned = 0;
        
        // Clean PHP session files older than 24 hours
        $sessionFiles = glob($tempDir . '/sess_*');
        foreach ($sessionFiles as $file) {
            if (filemtime($file) < time() - 86400) { // 24 hours
                if (unlink($file)) {
                    $cleaned++;
                }
            }
        }
        
        // Clean any temporary upload files
        $uploadTempFiles = glob('uploads/temp_*');
        foreach ($uploadTempFiles as $file) {
            if (filemtime($file) < time() - 3600) { // 1 hour
                if (unlink($file)) {
                    $cleaned++;
                }
            }
        }
        
        $maintenanceTasks['temp_files_cleanup'] = "success: cleaned {$cleaned} files";
        echo "   ✅ Cleaned {$cleaned} temporary files\n";
        $logger->info("Temporary files cleanup completed", ['cleaned_files' => $cleaned], 'maintenance');
    } catch (Exception $e) {
        $maintenanceTasks['temp_files_cleanup'] = 'failed: ' . $e->getMessage();
        echo "   ❌ Temporary files cleanup failed: " . $e->getMessage() . "\n";
        $logger->error("Temporary files cleanup failed", ['exception' => $e->getMessage()], 'maintenance');
    }
    
    // Task 7: Check system health
    echo "7. Performing system health check...\n";
    try {
        $healthIssues = [];
        
        // Check disk space
        $freeSpace = disk_free_space('.');
        $totalSpace = disk_total_space('.');
        $usagePercent = (($totalSpace - $freeSpace) / $totalSpace) * 100;
        
        if ($usagePercent > 90) {
            $healthIssues[] = "Disk usage critical: {$usagePercent}%";
        } elseif ($usagePercent > 80) {
            $healthIssues[] = "Disk usage warning: {$usagePercent}%";
        }
        
        // Check database connectivity
        $stmt = $db->query('SELECT 1');
        if (!$stmt) {
            $healthIssues[] = "Database connectivity issue";
        }
        
        // Check critical directories
        $criticalDirs = ['uploads', 'logs', 'config'];
        foreach ($criticalDirs as $dir) {
            if (!is_dir($dir) || !is_writable($dir)) {
                $healthIssues[] = "Directory issue: {$dir}";
            }
        }
        
        if (empty($healthIssues)) {
            $maintenanceTasks['health_check'] = 'success: all systems healthy';
            echo "   ✅ System health check passed\n";
        } else {
            $maintenanceTasks['health_check'] = 'warning: ' . implode(', ', $healthIssues);
            echo "   ⚠️ Health check warnings: " . implode(', ', $healthIssues) . "\n";
            $logger->warning("System health check found issues", ['issues' => $healthIssues], 'maintenance');
        }
    } catch (Exception $e) {
        $maintenanceTasks['health_check'] = 'failed: ' . $e->getMessage();
        echo "   ❌ Health check failed: " . $e->getMessage() . "\n";
        $logger->error("Health check failed", ['exception' => $e->getMessage()], 'maintenance');
    }
    
    // Task 8: Generate daily statistics
    echo "8. Generating daily statistics...\n";
    try {
        $today = date('Y-m-d');
        
        // Count today's registrations
        $stmt = $db->prepare("SELECT COUNT(*) as count FROM users WHERE DATE(registration_date) = ?");
        $stmt->execute([$today]);
        $todayRegistrations = $stmt->fetch()['count'];
        
        // Count today's PV transactions
        $stmt = $db->prepare("SELECT COUNT(*) as count, SUM(pv_amount) as total_pv FROM pv_transactions WHERE DATE(created_at) = ?");
        $stmt->execute([$today]);
        $pvStats = $stmt->fetch();
        
        // Count today's orders
        $stmt = $db->prepare("SELECT COUNT(*) as count, SUM(total_amount) as total_amount FROM purchase_orders WHERE DATE(created_at) = ? AND order_status = 'confirmed'");
        $stmt->execute([$today]);
        $orderStats = $stmt->fetch();
        
        $dailyStats = [
            'date' => $today,
            'registrations' => $todayRegistrations,
            'pv_transactions' => $pvStats['count'],
            'total_pv' => $pvStats['total_pv'] ?? 0,
            'orders' => $orderStats['count'],
            'order_amount' => $orderStats['total_amount'] ?? 0
        ];
        
        $maintenanceTasks['daily_statistics'] = 'success: generated statistics';
        echo "   ✅ Daily statistics generated\n";
        echo "      - Registrations: {$todayRegistrations}\n";
        echo "      - PV Transactions: {$pvStats['count']} (Total PV: {$pvStats['total_pv']})\n";
        echo "      - Orders: {$orderStats['count']} (Total: ₹{$orderStats['total_amount']})\n";
        
        $logger->info("Daily statistics generated", $dailyStats, 'maintenance');
    } catch (Exception $e) {
        $maintenanceTasks['daily_statistics'] = 'failed: ' . $e->getMessage();
        echo "   ❌ Daily statistics generation failed: " . $e->getMessage() . "\n";
        $logger->error("Daily statistics generation failed", ['exception' => $e->getMessage()], 'maintenance');
    }
    
    // Summary
    echo "\n" . str_repeat('-', 50) . "\n";
    echo "Daily maintenance completed!\n";
    echo "Summary:\n";
    
    $successCount = 0;
    $failureCount = 0;
    
    foreach ($maintenanceTasks as $task => $status) {
        $statusIcon = strpos($status, 'success') === 0 ? '✅' : (strpos($status, 'warning') === 0 ? '⚠️' : '❌');
        echo "- {$task}: {$statusIcon} {$status}\n";
        
        if (strpos($status, 'success') === 0) {
            $successCount++;
        } elseif (strpos($status, 'failed') === 0) {
            $failureCount++;
        }
    }
    
    echo "\nTasks completed: {$successCount} successful, {$failureCount} failed\n";
    echo str_repeat('-', 50) . "\n";
    
    // Log completion
    $logger->info("Daily maintenance completed", [
        'tasks' => $maintenanceTasks,
        'success_count' => $successCount,
        'failure_count' => $failureCount,
        'memory_usage' => memory_get_usage()
    ], 'maintenance');
    
} catch (Exception $e) {
    echo "Fatal error during maintenance: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
    
    $logger->critical("Daily maintenance fatal error", [
        'exception' => $e->getMessage(),
        'trace' => $e->getTraceAsString(),
        'memory_usage' => memory_get_usage()
    ], 'maintenance');
}

// Log completion
$logger->logCron('daily-maintenance', 'completed', ['memory_usage' => memory_get_usage()]);

echo str_repeat('=', 60) . "\n";
echo "Daily maintenance process completed\n";
echo "End time: " . date('Y-m-d H:i:s') . "\n";
echo "Peak memory usage: " . (memory_get_peak_usage(true) / 1024 / 1024) . " MB\n";
echo str_repeat('=', 60) . "\n";
?>
