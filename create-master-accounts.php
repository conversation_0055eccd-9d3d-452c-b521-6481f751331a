<?php
/**
 * Create Master User and Franchise User
 * MLM Binary Plan System
 */

require_once 'config/Connection.php';
require_once 'includes/BinaryTree.php';

try {
    $db = Database::getInstance();
    $pdo = $db->getConnection();
    
    echo "<h2>Creating Master User and Franchise User</h2>\n";
    
    // 1. Create Master User for User Panel
    echo "<h3>1. Creating Master User</h3>\n";
    
    // Check if master user already exists
    $checkUser = $pdo->prepare("SELECT user_id FROM users WHERE username = ? OR email = ?");
    $checkUser->execute(['master', '<EMAIL>']);
    
    if ($checkUser->fetch()) {
        echo "⚠️ Master user already exists!\n";
    } else {
        // Generate unique user ID
        $masterUserId = 'SP' . str_pad(1, 6, '0', STR_PAD_LEFT); // SP000001
        
        // Create master user
        $userStmt = $pdo->prepare("INSERT INTO users (user_id, username, email, password, full_name, phone, address, sponsor_id, franchise_id, placement_side, status) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
        $userStmt->execute([
            $masterUserId,
            'master',
            '<EMAIL>',
            'master123', // Plain text password as per your system
            'Master User',
            '+91-9999999999',
            'Master Address',
            null, // No sponsor for master user
            null, // No franchise for master user
            null, // No placement side for master user
            'active'
        ]);
        
        // Create wallet for master user
        $walletStmt = $pdo->prepare("INSERT INTO wallet (user_id, balance, total_earned, total_withdrawn) VALUES (?, ?, ?, ?)");
        $walletStmt->execute([$masterUserId, 0.00, 0.00, 0.00]);
        
        // Add to binary tree as root
        $binaryTree = new BinaryTree();
        $treeResult = $binaryTree->addUser($masterUserId, null, 'root', false);
        
        echo "✅ Master User created successfully!\n";
        echo "   User ID: {$masterUserId}\n";
        echo "   Username: master\n";
        echo "   Password: master123\n";
        echo "   Email: <EMAIL>\n";
        echo "   Login URL: user/login.php\n\n";
    }
    
    // 2. Create Franchise User for Franchise Panel
    echo "<h3>2. Creating Franchise User</h3>\n";
    
    // Check if franchise user already exists
    $checkFranchise = $pdo->prepare("SELECT id FROM franchise WHERE username = ? OR email = ?");
    $checkFranchise->execute(['franchise', '<EMAIL>']);
    
    if ($checkFranchise->fetch()) {
        echo "⚠️ Franchise user already exists!\n";
    } else {
        // Generate unique franchise code
        $franchiseCode = 'FR' . str_pad(1, 4, '0', STR_PAD_LEFT); // FR0001
        
        // Get admin ID for created_by field
        $adminStmt = $pdo->prepare("SELECT id FROM admin WHERE username = 'admin' LIMIT 1");
        $adminStmt->execute();
        $admin = $adminStmt->fetch();
        $adminId = $admin ? $admin['id'] : 1;
        
        // Create franchise user
        $franchiseStmt = $pdo->prepare("INSERT INTO franchise (franchise_code, username, email, password, full_name, phone, address, commission_rate, status, created_by) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
        $franchiseStmt->execute([
            $franchiseCode,
            'franchise',
            '<EMAIL>',
            'franchise123', // Plain text password as per your system
            'Master Franchise',
            '+91-8888888888',
            'Franchise Address',
            5.00, // 5% commission rate
            'active',
            $adminId
        ]);
        
        echo "✅ Franchise User created successfully!\n";
        echo "   Franchise Code: {$franchiseCode}\n";
        echo "   Username: franchise\n";
        echo "   Password: franchise123\n";
        echo "   Email: <EMAIL>\n";
        echo "   Login URL: franchise/login.php\n\n";
    }
    
    echo "<h3>Summary</h3>\n";
    echo "✅ Setup completed successfully!\n\n";
    echo "<strong>Master User (User Panel Access):</strong>\n";
    echo "- Login URL: <a href='user/login.php'>user/login.php</a>\n";
    echo "- Username: master\n";
    echo "- Password: master123\n\n";
    echo "<strong>Franchise User (Franchise Panel Access):</strong>\n";
    echo "- Login URL: <a href='franchise/login.php'>franchise/login.php</a>\n";
    echo "- Username: franchise\n";
    echo "- Password: franchise123\n\n";
    echo "<strong>Admin User (Admin Panel Access):</strong>\n";
    echo "- Login URL: <a href='admin/login.php'>admin/login.php</a>\n";
    echo "- Username: admin\n";
    echo "- Password: admin123\n\n";
    echo "<p><strong>Note:</strong> Please change these default passwords after first login for security.</p>\n";
    
} catch (PDOException $e) {
    die("❌ Error: " . $e->getMessage());
} catch (Exception $e) {
    die("❌ Error: " . $e->getMessage());
}
?>
