<?php
/**
 * Base Model Class
 * MLM Binary Plan System
 * 
 * This abstract class provides common functionality for all models
 */

abstract class BaseModel {
    protected $db;
    protected $table;
    protected $primaryKey = 'id';
    protected $fillable = [];
    protected $hidden = [];
    protected $timestamps = true;
    protected $created_at_field = 'created_at';
    protected $updated_at_field = 'updated_at';
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    /**
     * Find a record by primary key
     * 
     * @param mixed $id Primary key value
     * @return array|false Record data or false if not found
     */
    public function find($id) {
        $stmt = $this->db->prepare("SELECT * FROM {$this->table} WHERE {$this->primaryKey} = ?");
        $stmt->execute([$id]);
        return $stmt->fetch();
    }
    
    /**
     * Find a record by a specific field
     * 
     * @param string $field Field name
     * @param mixed $value Field value
     * @return array|false Record data or false if not found
     */
    public function findBy($field, $value) {
        $stmt = $this->db->prepare("SELECT * FROM {$this->table} WHERE {$field} = ?");
        $stmt->execute([$value]);
        return $stmt->fetch();
    }
    
    /**
     * Get all records
     * 
     * @param int $limit Maximum number of records to return
     * @param int $offset Offset for pagination
     * @param string $orderBy Field to order by
     * @param string $order Order direction (ASC or DESC)
     * @return array Records
     */
    public function all($limit = 1000, $offset = 0, $orderBy = null, $order = 'ASC') {
        $orderClause = '';
        if ($orderBy) {
            $orderClause = "ORDER BY {$orderBy} {$order}";
        } elseif ($this->timestamps) {
            $orderClause = "ORDER BY {$this->created_at_field} DESC";
        }
        
        $stmt = $this->db->prepare("SELECT * FROM {$this->table} {$orderClause} LIMIT ? OFFSET ?");
        $stmt->execute([$limit, $offset]);
        return $stmt->fetchAll();
    }
    
    /**
     * Get records by a specific field
     * 
     * @param string $field Field name
     * @param mixed $value Field value
     * @param int $limit Maximum number of records to return
     * @param int $offset Offset for pagination
     * @param string $orderBy Field to order by
     * @param string $order Order direction (ASC or DESC)
     * @return array Records
     */
    public function getBy($field, $value, $limit = 1000, $offset = 0, $orderBy = null, $order = 'ASC') {
        $orderClause = '';
        if ($orderBy) {
            $orderClause = "ORDER BY {$orderBy} {$order}";
        } elseif ($this->timestamps) {
            $orderClause = "ORDER BY {$this->created_at_field} DESC";
        }
        
        $stmt = $this->db->prepare("SELECT * FROM {$this->table} WHERE {$field} = ? {$orderClause} LIMIT ? OFFSET ?");
        $stmt->execute([$value, $limit, $offset]);
        return $stmt->fetchAll();
    }
    
    /**
     * Create a new record
     * 
     * @param array $data Record data
     * @return int|false Last insert ID or false on failure
     */
    public function create(array $data) {
        // Filter data to only include fillable fields
        $filteredData = array_intersect_key($data, array_flip($this->fillable));
        
        // Add timestamps if enabled
        if ($this->timestamps) {
            $filteredData[$this->created_at_field] = date('Y-m-d H:i:s');
            $filteredData[$this->updated_at_field] = date('Y-m-d H:i:s');
        }
        
        // Build query
        $fields = implode(', ', array_keys($filteredData));
        $placeholders = implode(', ', array_fill(0, count($filteredData), '?'));
        
        $stmt = $this->db->prepare("INSERT INTO {$this->table} ({$fields}) VALUES ({$placeholders})");
        $result = $stmt->execute(array_values($filteredData));
        
        return $result ? $this->db->lastInsertId() : false;
    }
    
    /**
     * Update a record
     * 
     * @param mixed $id Primary key value
     * @param array $data Record data
     * @return bool Success or failure
     */
    public function update($id, array $data) {
        // Filter data to only include fillable fields
        $filteredData = array_intersect_key($data, array_flip($this->fillable));
        
        // Add updated timestamp if enabled
        if ($this->timestamps) {
            $filteredData[$this->updated_at_field] = date('Y-m-d H:i:s');
        }
        
        // Build query
        $setClause = implode(' = ?, ', array_keys($filteredData)) . ' = ?';
        
        $stmt = $this->db->prepare("UPDATE {$this->table} SET {$setClause} WHERE {$this->primaryKey} = ?");
        $values = array_values($filteredData);
        $values[] = $id;
        
        return $stmt->execute($values);
    }
    
    /**
     * Delete a record
     * 
     * @param mixed $id Primary key value
     * @return bool Success or failure
     */
    public function delete($id) {
        $stmt = $this->db->prepare("DELETE FROM {$this->table} WHERE {$this->primaryKey} = ?");
        return $stmt->execute([$id]);
    }
    
    /**
     * Count records
     * 
     * @param string $field Field name for WHERE clause (optional)
     * @param mixed $value Field value for WHERE clause (optional)
     * @return int Number of records
     */
    public function count($field = null, $value = null) {
        if ($field) {
            $stmt = $this->db->prepare("SELECT COUNT(*) as count FROM {$this->table} WHERE {$field} = ?");
            $stmt->execute([$value]);
        } else {
            $stmt = $this->db->query("SELECT COUNT(*) as count FROM {$this->table}");
        }
        
        $result = $stmt->fetch();
        return (int) $result['count'];
    }
    
    /**
     * Begin a database transaction
     */
    public function beginTransaction() {
        return $this->db->beginTransaction();
    }
    
    /**
     * Commit a database transaction
     */
    public function commit() {
        return $this->db->commit();
    }
    
    /**
     * Rollback a database transaction
     */
    public function rollback() {
        return $this->db->rollback();
    }
    
    /**
     * Check if a transaction is active
     */
    public function inTransaction() {
        return $this->db->inTransaction();
    }
    
    /**
     * Execute a custom query
     * 
     * @param string $sql SQL query
     * @param array $params Query parameters
     * @return array|false Query results or false on failure
     */
    public function query($sql, array $params = []) {
        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);
        return $stmt->fetchAll();
    }
    
    /**
     * Execute a custom query and return a single row
     * 
     * @param string $sql SQL query
     * @param array $params Query parameters
     * @return array|false Query result or false on failure
     */
    public function queryOne($sql, array $params = []) {
        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);
        return $stmt->fetch();
    }
}
?>
