<?php
require_once 'config/Connection.php';
$db = Database::getInstance();

echo "=== USERS TABLE ===\n";
$users = $db->query('SELECT user_id, username, email, full_name, status FROM users ORDER BY id');
foreach($users as $user) {
    echo "User ID: {$user['user_id']}, Username: {$user['username']}, Email: {$user['email']}, Name: {$user['full_name']}, Status: {$user['status']}\n";
}

echo "\n=== FRANCHISE TABLE ===\n";
$franchises = $db->query('SELECT franchise_code, username, email, full_name, status FROM franchise ORDER BY id');
foreach($franchises as $franchise) {
    echo "Code: {$franchise['franchise_code']}, Username: {$franchise['username']}, Email: {$franchise['email']}, Name: {$franchise['full_name']}, Status: {$franchise['status']}\n";
}

echo "\n=== ADMIN TABLE ===\n";
$admins = $db->query('SELECT username, email, full_name, status FROM admin ORDER BY id');
foreach($admins as $admin) {
    echo "Username: {$admin['username']}, Email: {$admin['email']}, Name: {$admin['full_name']}, Status: {$admin['status']}\n";
}
?>
