<?php
/**
 * Admin Model
 * MLM Binary Plan System
 */

require_once 'BaseModel.php';

class Admin extends BaseModel {
    protected $table = 'admin';
    protected $primaryKey = 'id';
    protected $fillable = [
        'username', 'email', 'password', 'full_name', 'phone', 'status'
    ];
    protected $hidden = ['password'];
    
    /**
     * Find admin by username
     * 
     * @param string $username Username
     * @return array|false Admin data or false if not found
     */
    public function findByUsername($username) {
        return $this->findBy('username', $username);
    }
    
    /**
     * Find admin by email
     * 
     * @param string $email Email
     * @return array|false Admin data or false if not found
     */
    public function findByEmail($email) {
        return $this->findBy('email', $email);
    }
    
    /**
     * Authenticate admin
     * 
     * @param string $username Username or email
     * @param string $password Password
     * @return array|false Admin data or false if authentication fails
     */
    public function authenticate($username, $password) {
        // Check if username is an email
        $isEmail = filter_var($username, FILTER_VALIDATE_EMAIL);
        
        // Find admin by username or email
        if ($isEmail) {
            $admin = $this->findByEmail($username);
        } else {
            $admin = $this->findByUsername($username);
        }
        
        // Check if admin exists
        if (!$admin) {
            return false;
        }
        
        // Check if admin is active
        if ($admin['status'] !== 'active') {
            return false;
        }
        
        // Check password (plain text as requested by user)
        if ($admin['password'] !== $password) {
            return false;
        }
        
        // Remove password from admin data
        unset($admin['password']);
        
        return $admin;
    }
    
    /**
     * Get system statistics
     * 
     * @return array System statistics
     */
    public function getSystemStats() {
        $stats = [];
        
        // Total users
        $stmt = $this->db->query("SELECT COUNT(*) as count FROM users");
        $result = $stmt->fetch();
        $stats['total_users'] = (int) $result['count'];
        
        // Active users
        $stmt = $this->db->query("SELECT COUNT(*) as count FROM users WHERE status = 'active'");
        $result = $stmt->fetch();
        $stats['active_users'] = (int) $result['count'];
        
        // Total franchises
        $stmt = $this->db->query("SELECT COUNT(*) as count FROM franchise");
        $result = $stmt->fetch();
        $stats['total_franchises'] = (int) $result['count'];
        
        // Total products
        $stmt = $this->db->query("SELECT COUNT(*) as count FROM products WHERE status = 'active'");
        $result = $stmt->fetch();
        $stats['total_products'] = (int) $result['count'];
        
        // Total PV transactions today
        $stmt = $this->db->query("SELECT COUNT(*) as count, SUM(pv_amount) as total_pv FROM pv_transactions WHERE DATE(created_at) = CURDATE()");
        $result = $stmt->fetch();
        $stats['today_transactions'] = (int) $result['count'];
        $stats['today_pv'] = (float) ($result['total_pv'] ?? 0);
        
        // Total wallet balance
        $stmt = $this->db->query("SELECT SUM(balance) as total_balance FROM wallet");
        $result = $stmt->fetch();
        $stats['total_wallet_balance'] = (float) ($result['total_balance'] ?? 0);
        
        // Pending withdrawals
        $stmt = $this->db->query("SELECT COUNT(*) as count, SUM(amount) as total_amount FROM withdrawals WHERE status = 'pending'");
        $result = $stmt->fetch();
        $stats['pending_withdrawals'] = (int) $result['count'];
        $stats['pending_withdrawal_amount'] = (float) ($result['total_amount'] ?? 0);
        
        return $stats;
    }
    
    /**
     * Get recent activities
     * 
     * @param int $limit Maximum number of activities to return
     * @return array Recent activities
     */
    public function getRecentActivities($limit = 20) {
        $activities = [];
        
        // Recent user registrations
        $stmt = $this->db->prepare("SELECT user_id, full_name, registration_date, 'user_registration' as activity_type FROM users ORDER BY registration_date DESC LIMIT ?");
        $stmt->execute([$limit]);
        $userRegistrations = $stmt->fetchAll();
        
        foreach ($userRegistrations as $registration) {
            $activities[] = [
                'type' => 'user_registration',
                'message' => "New user registered: {$registration['full_name']} ({$registration['user_id']})",
                'timestamp' => $registration['registration_date']
            ];
        }
        
        // Recent PV transactions
        $stmt = $this->db->prepare("SELECT pt.*, u.full_name FROM pv_transactions pt JOIN users u ON pt.user_id = u.user_id ORDER BY pt.created_at DESC LIMIT ?");
        $stmt->execute([$limit]);
        $pvTransactions = $stmt->fetchAll();
        
        foreach ($pvTransactions as $transaction) {
            $activities[] = [
                'type' => 'pv_transaction',
                'message' => "PV transaction: {$transaction['full_name']} - {$transaction['pv_amount']} PV ({$transaction['side']} side)",
                'timestamp' => $transaction['created_at']
            ];
        }
        
        // Recent withdrawals
        $stmt = $this->db->prepare("SELECT w.*, u.full_name FROM withdrawals w JOIN users u ON w.user_id = u.user_id ORDER BY w.requested_at DESC LIMIT ?");
        $stmt->execute([$limit]);
        $withdrawals = $stmt->fetchAll();
        
        foreach ($withdrawals as $withdrawal) {
            $activities[] = [
                'type' => 'withdrawal',
                'message' => "Withdrawal request: {$withdrawal['full_name']} - ₹{$withdrawal['amount']} ({$withdrawal['status']})",
                'timestamp' => $withdrawal['requested_at']
            ];
        }
        
        // Sort activities by timestamp
        usort($activities, function($a, $b) {
            return strtotime($b['timestamp']) - strtotime($a['timestamp']);
        });
        
        return array_slice($activities, 0, $limit);
    }
    
    /**
     * Get user growth data for charts
     * 
     * @param int $days Number of days to get data for
     * @return array User growth data
     */
    public function getUserGrowthData($days = 30) {
        $stmt = $this->db->prepare("
            SELECT DATE(registration_date) as date, COUNT(*) as count 
            FROM users 
            WHERE registration_date >= DATE_SUB(CURDATE(), INTERVAL ? DAY)
            GROUP BY DATE(registration_date)
            ORDER BY date ASC
        ");
        $stmt->execute([$days]);
        return $stmt->fetchAll();
    }
    
    /**
     * Get PV transaction data for charts
     * 
     * @param int $days Number of days to get data for
     * @return array PV transaction data
     */
    public function getPVTransactionData($days = 30) {
        $stmt = $this->db->prepare("
            SELECT DATE(created_at) as date, SUM(pv_amount) as total_pv, COUNT(*) as count
            FROM pv_transactions 
            WHERE created_at >= DATE_SUB(CURDATE(), INTERVAL ? DAY)
            GROUP BY DATE(created_at)
            ORDER BY date ASC
        ");
        $stmt->execute([$days]);
        return $stmt->fetchAll();
    }
    
    /**
     * Get income distribution data
     * 
     * @param int $days Number of days to get data for
     * @return array Income distribution data
     */
    public function getIncomeDistributionData($days = 30) {
        $stmt = $this->db->prepare("
            SELECT DATE(created_at) as date, SUM(amount) as total_income, COUNT(*) as count
            FROM wallet_transactions 
            WHERE transaction_type = 'credit' 
            AND reference_type = 'pv_matching'
            AND created_at >= DATE_SUB(CURDATE(), INTERVAL ? DAY)
            GROUP BY DATE(created_at)
            ORDER BY date ASC
        ");
        $stmt->execute([$days]);
        return $stmt->fetchAll();
    }
}
?>
