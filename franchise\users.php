<?php
/**
 * Franchise User Management
 * MLM Binary Plan System
 */

require_once '../includes/header.php';
require_once '../includes/Auth.php';
require_once '../includes/PVSystem.php';
require_once '../includes/Wallet.php';

// Require franchise authentication
if (!isLoggedIn('franchise')) {
    Response::redirect('login.php');
}

$currentUser = getCurrentUser();
$franchiseId = getCurrentUserId();

// Get franchise details
$db = Database::getInstance();
$franchiseStmt = $db->prepare("SELECT * FROM franchise WHERE id = ?");
$franchiseStmt->execute([$franchiseId]);
$franchiseDetails = $franchiseStmt->fetch();

// Get search and filter parameters
$search = $_GET['search'] ?? '';
$status = $_GET['status'] ?? '';
$page = (int) ($_GET['page'] ?? 1);
$limit = 20;
$offset = ($page - 1) * $limit;

// Build query
$whereClause = "WHERE u.franchise_id = ?";
$params = [$franchiseId];

if ($search) {
    $whereClause .= " AND (u.full_name LIKE ? OR u.email LIKE ? OR u.user_id LIKE ? OR u.username LIKE ?)";
    $searchParam = "%{$search}%";
    $params = array_merge($params, [$searchParam, $searchParam, $searchParam, $searchParam]);
}

if ($status) {
    $whereClause .= " AND u.status = ?";
    $params[] = $status;
}

// Get users
$usersStmt = $db->prepare("
    SELECT u.*, 
           w.balance as wallet_balance,
           (SELECT SUM(pv_amount) FROM pv_transactions WHERE user_id = u.user_id AND side = 'left') as left_pv,
           (SELECT SUM(pv_amount) FROM pv_transactions WHERE user_id = u.user_id AND side = 'right') as right_pv,
           (SELECT full_name FROM users WHERE user_id = u.sponsor_id) as sponsor_name
    FROM users u
    LEFT JOIN wallet w ON u.user_id = w.user_id
    {$whereClause}
    ORDER BY u.registration_date DESC
    LIMIT ? OFFSET ?
");
$params[] = $limit;
$params[] = $offset;
$usersStmt->execute($params);
$users = $usersStmt->fetchAll();

// Get total count for pagination
$countParams = array_slice($params, 0, -2); // Remove limit and offset
$countStmt = $db->prepare("SELECT COUNT(*) FROM users u {$whereClause}");
$countStmt->execute($countParams);
$totalUsers = $countStmt->fetchColumn();
$totalPages = ceil($totalUsers / $limit);

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User Management - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
    <style>
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        }
        .status-badge {
            padding: 0.25rem 0.5rem;
            border-radius: 0.375rem;
            font-size: 0.75rem;
            font-weight: 500;
        }
        .status-active { background-color: #d1edff; color: #0c63e4; }
        .status-inactive { background-color: #f8d7da; color: #721c24; }
        .status-suspended { background-color: #fff3cd; color: #856404; }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 sidebar p-0">
                <div class="p-3">
                    <div class="text-center text-white mb-4">
                        <i class="fas fa-store fa-3x mb-2"></i>
                        <h5><?php echo htmlspecialchars($franchiseDetails['full_name']); ?></h5>
                        <small><?php echo htmlspecialchars($franchiseDetails['franchise_code']); ?></small>
                    </div>
                    
                    <nav class="nav flex-column">
                        <a class="nav-link text-white" href="dashboard.php">
                            <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                        </a>
                        <a class="nav-link text-white active" href="users.php">
                            <i class="fas fa-users me-2"></i>Manage Users
                        </a>
                        <a class="nav-link text-white" href="products.php">
                            <i class="fas fa-box me-2"></i>Product Assignment
                        </a>
                        <a class="nav-link text-white" href="pv-transactions.php">
                            <i class="fas fa-chart-line me-2"></i>PV Transactions
                        </a>
                        <a class="nav-link text-white" href="billing.php">
                            <i class="fas fa-file-invoice-dollar me-2"></i>Billing & Invoices
                        </a>
                        <a class="nav-link text-white" href="reports.php">
                            <i class="fas fa-chart-bar me-2"></i>Reports
                        </a>
                        <a class="nav-link text-white" href="profile.php">
                            <i class="fas fa-user me-2"></i>Profile
                        </a>
                        <hr class="text-white">
                        <a class="nav-link text-white" href="../logout.php">
                            <i class="fas fa-sign-out-alt me-2"></i>Logout
                        </a>
                    </nav>
                </div>
            </div>
            
            <!-- Main Content -->
            <div class="col-md-9 col-lg-10">
                <div class="p-4">
                    <!-- Header -->
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h2>User Management</h2>
                        <a href="../user/register.php" class="btn btn-primary">
                            <i class="fas fa-user-plus me-2"></i>Add New User
                        </a>
                    </div>
                    
                    <!-- Filters -->
                    <div class="card mb-4">
                        <div class="card-body">
                            <form method="GET" action="">
                                <div class="row">
                                    <div class="col-md-4 mb-3">
                                        <label for="search" class="form-label">Search</label>
                                        <input type="text" class="form-control" id="search" name="search" 
                                               value="<?php echo htmlspecialchars($search); ?>" 
                                               placeholder="Name, email, user ID...">
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <label for="status" class="form-label">Status</label>
                                        <select class="form-select" id="status" name="status">
                                            <option value="">All Status</option>
                                            <option value="active" <?php echo $status === 'active' ? 'selected' : ''; ?>>Active</option>
                                            <option value="inactive" <?php echo $status === 'inactive' ? 'selected' : ''; ?>>Inactive</option>
                                            <option value="suspended" <?php echo $status === 'suspended' ? 'selected' : ''; ?>>Suspended</option>
                                        </select>
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <label class="form-label">&nbsp;</label>
                                        <div>
                                            <button type="submit" class="btn btn-primary">
                                                <i class="fas fa-search me-2"></i>Search
                                            </button>
                                            <a href="users.php" class="btn btn-outline-secondary">
                                                <i class="fas fa-times me-2"></i>Clear
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                    
                    <!-- Users Table -->
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">Users (<?php echo $totalUsers; ?>)</h5>
                            <div class="text-muted">
                                Page <?php echo $page; ?> of <?php echo $totalPages; ?>
                            </div>
                        </div>
                        <div class="card-body">
                            <?php if (!empty($users)): ?>
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>User Details</th>
                                                <th>Contact</th>
                                                <th>Sponsor</th>
                                                <th>PV Status</th>
                                                <th>Wallet</th>
                                                <th>Status</th>
                                                <th>Registered</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($users as $user): ?>
                                                <tr>
                                                    <td>
                                                        <strong><?php echo htmlspecialchars($user['full_name']); ?></strong><br>
                                                        <small class="text-muted">
                                                            ID: <?php echo htmlspecialchars($user['user_id']); ?><br>
                                                            Username: <?php echo htmlspecialchars($user['username']); ?>
                                                        </small>
                                                    </td>
                                                    <td>
                                                        <?php echo htmlspecialchars($user['email']); ?><br>
                                                        <small class="text-muted"><?php echo htmlspecialchars($user['phone']); ?></small>
                                                    </td>
                                                    <td>
                                                        <?php if ($user['sponsor_name']): ?>
                                                            <?php echo htmlspecialchars($user['sponsor_name']); ?><br>
                                                            <small class="text-muted"><?php echo htmlspecialchars($user['sponsor_id']); ?></small>
                                                        <?php else: ?>
                                                            <span class="text-muted">Root User</span>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td>
                                                        <div class="d-flex">
                                                            <div class="me-2">
                                                                <small class="text-info">L:</small> <?php echo formatPV($user['left_pv'] ?? 0); ?>
                                                            </div>
                                                            <div>
                                                                <small class="text-warning">R:</small> <?php echo formatPV($user['right_pv'] ?? 0); ?>
                                                            </div>
                                                        </div>
                                                        <small class="text-muted">
                                                            Side: <?php echo ucfirst($user['placement_side'] ?? 'N/A'); ?>
                                                        </small>
                                                    </td>
                                                    <td>
                                                        <strong><?php echo formatCurrency($user['wallet_balance'] ?? 0); ?></strong>
                                                    </td>
                                                    <td>
                                                        <span class="status-badge status-<?php echo $user['status']; ?>">
                                                            <?php echo ucfirst($user['status']); ?>
                                                        </span>
                                                    </td>
                                                    <td><?php echo date('M d, Y', strtotime($user['registration_date'])); ?></td>
                                                    <td>
                                                        <div class="btn-group btn-group-sm">
                                                            <button class="btn btn-outline-primary" onclick="viewUser('<?php echo $user['user_id']; ?>')" title="View Details">
                                                                <i class="fas fa-eye"></i>
                                                            </button>
                                                            <a href="products.php?user_id=<?php echo $user['user_id']; ?>" class="btn btn-outline-success" title="Assign Product">
                                                                <i class="fas fa-box"></i>
                                                            </a>
                                                            <button class="btn btn-outline-info" onclick="viewPVHistory('<?php echo $user['user_id']; ?>')" title="PV History">
                                                                <i class="fas fa-chart-line"></i>
                                                            </button>
                                                        </div>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                                
                                <!-- Pagination -->
                                <?php if ($totalPages > 1): ?>
                                    <nav class="mt-4">
                                        <ul class="pagination justify-content-center">
                                            <?php if ($page > 1): ?>
                                                <li class="page-item">
                                                    <a class="page-link" href="?page=<?php echo $page - 1; ?>&search=<?php echo urlencode($search); ?>&status=<?php echo urlencode($status); ?>">Previous</a>
                                                </li>
                                            <?php endif; ?>
                                            
                                            <?php for ($i = max(1, $page - 2); $i <= min($totalPages, $page + 2); $i++): ?>
                                                <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                                                    <a class="page-link" href="?page=<?php echo $i; ?>&search=<?php echo urlencode($search); ?>&status=<?php echo urlencode($status); ?>"><?php echo $i; ?></a>
                                                </li>
                                            <?php endfor; ?>
                                            
                                            <?php if ($page < $totalPages): ?>
                                                <li class="page-item">
                                                    <a class="page-link" href="?page=<?php echo $page + 1; ?>&search=<?php echo urlencode($search); ?>&status=<?php echo urlencode($status); ?>">Next</a>
                                                </li>
                                            <?php endif; ?>
                                        </ul>
                                    </nav>
                                <?php endif; ?>
                                
                            <?php else: ?>
                                <div class="text-center py-4">
                                    <i class="fas fa-users fa-3x text-muted mb-3"></i>
                                    <p class="text-muted">No users found</p>
                                    <a href="../user/register.php" class="btn btn-primary">Add First User</a>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- User Details Modal -->
    <div class="modal fade" id="userDetailsModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">User Details</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="userDetailsContent">
                    <!-- Content will be loaded via AJAX -->
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function viewUser(userId) {
            // Show modal with user details
            const modal = new bootstrap.Modal(document.getElementById('userDetailsModal'));
            document.getElementById('userDetailsContent').innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin"></i> Loading...</div>';
            modal.show();
            
            // Load user details via AJAX (you can implement this)
            setTimeout(() => {
                document.getElementById('userDetailsContent').innerHTML = `
                    <p>User details for ${userId} would be loaded here.</p>
                    <p>This can include:</p>
                    <ul>
                        <li>Complete profile information</li>
                        <li>PV transaction history</li>
                        <li>Wallet transaction history</li>
                        <li>Binary tree position</li>
                        <li>Downline information</li>
                    </ul>
                `;
            }, 1000);
        }
        
        function viewPVHistory(userId) {
            // Redirect to PV transactions page with user filter
            window.location.href = `pv-transactions.php?user_id=${userId}`;
        }
    </script>
</body>
</html>
