<?php
/**
 * Wallet Management Class
 * MLM Binary Plan System
 */

class Wallet {
    private $db;
    private $config;
    
    public function __construct() {
        $this->db = Database::getInstance();
        $this->config = Config::getInstance();
    }
    
    /**
     * Get user's wallet information
     */
    public function getWallet($userId) {
        $stmt = $this->db->prepare("SELECT * FROM wallet WHERE user_id = ?");
        $stmt->execute([$userId]);
        $wallet = $stmt->fetch();
        
        if (!$wallet) {
            // Create wallet if doesn't exist
            $this->createWallet($userId);
            return $this->getWallet($userId);
        }
        
        return $wallet;
    }
    
    /**
     * Create wallet for user
     */
    public function createWallet($userId) {
        $stmt = $this->db->prepare("INSERT INTO wallet (user_id, balance, total_earned, total_withdrawn) VALUES (?, 0.00, 0.00, 0.00)");
        return $stmt->execute([$userId]);
    }
    
    /**
     * Credit amount to wallet
     */
    public function credit($userId, $amount, $description, $referenceType = 'manual', $referenceId = null) {
        try {
            $this->db->beginTransaction();
            
            $wallet = $this->getWallet($userId);
            $balanceBefore = $wallet['balance'];
            $balanceAfter = $balanceBefore + $amount;
            
            // Update wallet
            $updateStmt = $this->db->prepare("UPDATE wallet SET balance = ?, total_earned = total_earned + ? WHERE user_id = ?");
            $updateStmt->execute([$balanceAfter, $amount, $userId]);
            
            // Record transaction
            $transStmt = $this->db->prepare("INSERT INTO wallet_transactions (user_id, transaction_type, amount, description, reference_type, reference_id, balance_before, balance_after) VALUES (?, 'credit', ?, ?, ?, ?, ?, ?)");
            $transStmt->execute([$userId, $amount, $description, $referenceType, $referenceId, $balanceBefore, $balanceAfter]);
            
            $this->db->commit();
            return true;
            
        } catch (Exception $e) {
            $this->db->rollback();
            error_log("Wallet credit error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Debit amount from wallet
     */
    public function debit($userId, $amount, $description, $referenceType = 'manual', $referenceId = null) {
        try {
            $this->db->beginTransaction();
            
            $wallet = $this->getWallet($userId);
            $balanceBefore = $wallet['balance'];
            
            if ($balanceBefore < $amount) {
                throw new Exception("Insufficient balance");
            }
            
            $balanceAfter = $balanceBefore - $amount;
            
            // Update wallet
            $updateStmt = $this->db->prepare("UPDATE wallet SET balance = ?, total_withdrawn = total_withdrawn + ? WHERE user_id = ?");
            $updateStmt->execute([$balanceAfter, $amount, $userId]);
            
            // Record transaction
            $transStmt = $this->db->prepare("INSERT INTO wallet_transactions (user_id, transaction_type, amount, description, reference_type, reference_id, balance_before, balance_after) VALUES (?, 'debit', ?, ?, ?, ?, ?, ?)");
            $transStmt->execute([$userId, $amount, $description, $referenceType, $referenceId, $balanceBefore, $balanceAfter]);
            
            $this->db->commit();
            return true;
            
        } catch (Exception $e) {
            $this->db->rollback();
            error_log("Wallet debit error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get wallet transaction history
     */
    public function getTransactionHistory($userId, $limit = 50, $offset = 0) {
        $stmt = $this->db->prepare("SELECT * FROM wallet_transactions WHERE user_id = ? ORDER BY created_at DESC LIMIT ? OFFSET ?");
        $stmt->execute([$userId, $limit, $offset]);
        return $stmt->fetchAll();
    }
    
    /**
     * Request withdrawal
     */
    public function requestWithdrawal($userId, $amount, $bankDetails) {
        try {
            $this->db->beginTransaction();
            
            $wallet = $this->getWallet($userId);
            $minWithdrawal = $this->config->getMinWithdrawal();
            
            // Validate withdrawal
            if ($amount < $minWithdrawal) {
                throw new Exception("Minimum withdrawal amount is " . formatCurrency($minWithdrawal));
            }
            
            if ($wallet['balance'] < $amount) {
                throw new Exception("Insufficient balance");
            }
            
            // Check for pending withdrawals
            $pendingStmt = $this->db->prepare("SELECT COUNT(*) FROM withdrawals WHERE user_id = ? AND status = 'pending'");
            $pendingStmt->execute([$userId]);
            if ($pendingStmt->fetchColumn() > 0) {
                throw new Exception("You have a pending withdrawal request");
            }
            
            // Create withdrawal request
            $withdrawalStmt = $this->db->prepare("INSERT INTO withdrawals (user_id, amount, bank_details, status) VALUES (?, ?, ?, 'pending')");
            $withdrawalStmt->execute([$userId, $amount, json_encode($bankDetails)]);
            
            $this->db->commit();
            return true;
            
        } catch (Exception $e) {
            $this->db->rollback();
            throw $e;
        }
    }
    
    /**
     * Process withdrawal (admin function)
     */
    public function processWithdrawal($withdrawalId, $status, $adminId, $adminNotes = '') {
        try {
            $this->db->beginTransaction();
            
            // Get withdrawal details
            $withdrawalStmt = $this->db->prepare("SELECT * FROM withdrawals WHERE id = ?");
            $withdrawalStmt->execute([$withdrawalId]);
            $withdrawal = $withdrawalStmt->fetch();
            
            if (!$withdrawal) {
                throw new Exception("Withdrawal not found");
            }
            
            if ($withdrawal['status'] !== 'pending') {
                throw new Exception("Withdrawal already processed");
            }
            
            // Update withdrawal status
            $updateStmt = $this->db->prepare("UPDATE withdrawals SET status = ?, processed_by = ?, admin_notes = ?, processed_at = NOW() WHERE id = ?");
            $updateStmt->execute([$status, $adminId, $adminNotes, $withdrawalId]);
            
            if ($status === 'approved') {
                // Debit from wallet
                $this->debit($withdrawal['user_id'], $withdrawal['amount'], 'Withdrawal processed', 'withdrawal', $withdrawalId);
            }
            
            $this->db->commit();
            return true;
            
        } catch (Exception $e) {
            $this->db->rollback();
            throw $e;
        }
    }
    
    /**
     * Get withdrawal history
     */
    public function getWithdrawalHistory($userId, $limit = 50, $offset = 0) {
        $stmt = $this->db->prepare("SELECT * FROM withdrawals WHERE user_id = ? ORDER BY requested_at DESC LIMIT ? OFFSET ?");
        $stmt->execute([$userId, $limit, $offset]);
        return $stmt->fetchAll();
    }
    
    /**
     * Get pending withdrawals (admin function)
     */
    public function getPendingWithdrawals($limit = 50, $offset = 0) {
        $stmt = $this->db->prepare("
            SELECT w.*, u.full_name, u.email, u.phone 
            FROM withdrawals w 
            JOIN users u ON w.user_id = u.user_id 
            WHERE w.status = 'pending' 
            ORDER BY w.requested_at ASC 
            LIMIT ? OFFSET ?
        ");
        $stmt->execute([$limit, $offset]);
        return $stmt->fetchAll();
    }
    
    /**
     * Get wallet statistics (admin function)
     */
    public function getWalletStatistics() {
        $stmt = $this->db->prepare("
            SELECT 
                COUNT(*) as total_wallets,
                SUM(balance) as total_balance,
                SUM(total_earned) as total_earned,
                SUM(total_withdrawn) as total_withdrawn,
                AVG(balance) as avg_balance
            FROM wallet
        ");
        $stmt->execute();
        return $stmt->fetch();
    }
    
    /**
     * Get top earners
     */
    public function getTopEarners($limit = 10) {
        $stmt = $this->db->prepare("
            SELECT w.*, u.full_name, u.user_id 
            FROM wallet w 
            JOIN users u ON w.user_id = u.user_id 
            ORDER BY w.total_earned DESC 
            LIMIT ?
        ");
        $stmt->execute([$limit]);
        return $stmt->fetchAll();
    }
    
    /**
     * Transfer between wallets (admin function)
     */
    public function transfer($fromUserId, $toUserId, $amount, $description) {
        try {
            $this->db->beginTransaction();
            
            // Debit from sender
            $this->debit($fromUserId, $amount, "Transfer to $toUserId: $description", 'transfer');
            
            // Credit to receiver
            $this->credit($toUserId, $amount, "Transfer from $fromUserId: $description", 'transfer');
            
            $this->db->commit();
            return true;
            
        } catch (Exception $e) {
            $this->db->rollback();
            throw $e;
        }
    }
    
    /**
     * Get wallet balance
     */
    public function getBalance($userId) {
        $wallet = $this->getWallet($userId);
        return (float) $wallet['balance'];
    }
    
    /**
     * Check if user has sufficient balance
     */
    public function hasSufficientBalance($userId, $amount) {
        return $this->getBalance($userId) >= $amount;
    }
}
?>
