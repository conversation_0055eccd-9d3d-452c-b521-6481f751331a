# 🔧 Troubleshooting Guide
## ShaktiPure MLM Binary Plan System

This guide helps you resolve common issues with the MLM system.

## 🚨 Internal Server Error (500)

If you're experiencing a 500 Internal Server Error, follow these steps:

### Step 1: Check Apache/PHP Error Logs
```bash
# Check Apache error log
tail -f /var/log/apache2/error.log

# Check PHP error log
tail -f /var/log/php/error.log

# For XAMPP on Windows
tail -f C:\xampp\apache\logs\error.log
```

### Step 2: Enable PHP Error Reporting
Add this to the top of your index.php file temporarily:
```php
error_reporting(E_ALL);
ini_set('display_errors', 1);
```

### Step 3: Check File Permissions
```bash
# Set correct permissions
chmod 755 /path/to/shaktipure
chmod -R 644 /path/to/shaktipure/*
chmod -R 755 /path/to/shaktipure/uploads/
chmod -R 755 /path/to/shaktipure/logs/
```

### Step 4: Verify Database Connection
Create a test file `db_test.php`:
```php
<?php
require_once 'config/database.php';
require_once 'config/Connection.php';

try {
    $db = Database::getInstance();
    $stmt = $db->query("SELECT 1 as test");
    $result = $stmt->fetch();
    echo "✅ Database connection successful";
} catch (Exception $e) {
    echo "❌ Database error: " . $e->getMessage();
}
?>
```

### Step 5: Check .htaccess File
If the error persists, temporarily rename `.htaccess` to `.htaccess_backup` and test again.

## 🗄️ Database Issues

### Connection Failed
1. Check database credentials in `config/database.php`
2. Ensure MySQL service is running
3. Verify database exists and user has proper permissions

### Table Missing Errors
Run the setup script:
```bash
php setup.php
```

### Performance Issues
Run the database optimization:
```bash
php optimize_database.php
```

## 📁 File Permission Issues

### Uploads Not Working
```bash
chmod 755 uploads/
chmod 755 uploads/products/
```

### Logs Not Writing
```bash
chmod 755 logs/
touch logs/error.log
chmod 644 logs/error.log
```

## 🔐 Session Issues

### Session Not Starting
1. Check if session directory is writable
2. Verify session configuration in php.ini
3. Clear browser cookies and cache

### Login Redirects Not Working
1. Check if headers are already sent
2. Verify session functions are working
3. Check for output before header() calls

## 🌐 Apache Configuration Issues

### Mod_rewrite Not Working
Enable mod_rewrite:
```bash
sudo a2enmod rewrite
sudo systemctl restart apache2
```

### .htaccess Not Working
Add to Apache virtual host:
```apache
<Directory /path/to/shaktipure>
    AllowOverride All
</Directory>
```

## 🔧 PHP Configuration Issues

### Missing Extensions
Install required PHP extensions:
```bash
# Ubuntu/Debian
sudo apt install php-pdo php-mysql php-gd php-curl php-mbstring

# CentOS/RHEL
sudo yum install php-pdo php-mysql php-gd php-curl php-mbstring
```

### Memory Limit Issues
Increase PHP memory limit in php.ini:
```ini
memory_limit = 256M
max_execution_time = 60
```

## 📊 Performance Issues

### Slow Page Loading
1. Check database query performance
2. Enable query caching
3. Optimize images and assets
4. Check server resources

### High Memory Usage
1. Review cron job memory usage
2. Optimize database queries
3. Clear old log files
4. Check for memory leaks

## 🔍 Debugging Steps

### Enable Debug Mode
Add to `config/database.php`:
```php
define('DEBUG_MODE', true);
```

### Check System Health
Visit: `http://yoursite.com/health-check.php`

### Review Logs
Check these log files:
- `logs/error.log` - Application errors
- `logs/auth.log` - Authentication issues
- `logs/pv.log` - PV transaction issues
- `logs/cron.log` - Cron job issues

## 🆘 Emergency Recovery

### Restore from Backup
```bash
# Restore database
mysql -u username -p database_name < backup.sql

# Restore files
tar -xzf backup.tar.gz -C /path/to/shaktipure/
```

### Reset Admin Password
Run this SQL query:
```sql
UPDATE admin SET password = 'newpassword' WHERE username = 'admin';
```

### Clear All Sessions
```bash
rm -rf /tmp/sess_*
```

## 📞 Getting Help

### Check Documentation
1. README.md - General information
2. DEPLOYMENT_GUIDE.md - Deployment instructions
3. OPTIMIZATION_SUMMARY.md - Optimization details

### System Information
Gather this information when seeking help:
- PHP version: `php -v`
- MySQL version: `mysql --version`
- Apache version: `apache2 -v`
- Operating system: `uname -a`
- Error messages from logs
- Steps to reproduce the issue

### Contact Support
If you need additional help:
1. Check the error logs first
2. Try the troubleshooting steps above
3. Gather system information
4. Document the exact error message
5. Note what you were doing when the error occurred

## ✅ Prevention

### Regular Maintenance
1. Run daily maintenance cron job
2. Monitor disk space and memory usage
3. Keep backups current
4. Update system packages regularly
5. Monitor error logs weekly

### Security Best Practices
1. Keep software updated
2. Use strong passwords
3. Monitor login attempts
4. Regular security audits
5. Backup regularly

### Performance Monitoring
1. Monitor page load times
2. Check database performance
3. Monitor server resources
4. Review cron job performance
5. Optimize queries regularly
