<?php
/**
 * Common Functions
 * MLM Binary Plan System
 */

/**
 * Generate unique user ID
 */
function generateUserId($prefix = 'SP') {
    return $prefix . date('Y') . str_pad(mt_rand(1, 9999), 4, '0', STR_PAD_LEFT);
}

/**
 * Generate unique franchise code
 */
function generateFranchiseCode($prefix = 'FR') {
    return $prefix . date('Y') . str_pad(mt_rand(1, 999), 3, '0', STR_PAD_LEFT);
}

/**
 * Generate unique order ID
 */
function generateOrderId($prefix = 'ORD') {
    return $prefix . date('YmdHis') . mt_rand(100, 999);
}

/**
 * Validate email format
 */
function isValidEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

/**
 * Validate phone number (Indian format)
 */
function isValidPhone($phone) {
    $pattern = '/^[6-9]\d{9}$/';
    return preg_match($pattern, $phone);
}

/**
 * Sanitize input data
 */
function sanitizeInput($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data);
    return $data;
}

/**
 * Format currency
 */
function formatCurrency($amount) {
    return '₹' . number_format($amount, 2);
}

/**
 * Format PV
 */
function formatPV($pv) {
    return number_format($pv, 2) . ' PV';
}

/**
 * Check if user is logged in
 */
function isLoggedIn($userType = null) {
    if (!isset($_SESSION['user_id']) || !isset($_SESSION['user_type'])) {
        return false;
    }
    
    if ($userType && $_SESSION['user_type'] !== $userType) {
        return false;
    }
    
    return true;
}

/**
 * Redirect to login if not authenticated
 */
function requireLogin($userType = null) {
    if (!isLoggedIn($userType)) {
        $loginPage = $userType ? "login_{$userType}.php" : 'login.php';
        header("Location: {$loginPage}");
        exit();
    }
}

/**
 * Generate CSRF token
 */
function generateCSRFToken() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    return $_SESSION['csrf_token'];
}

/**
 * Verify CSRF token
 */
if (!function_exists('verifyCSRFToken')) {
    function verifyCSRFToken($token) {
        return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
    }
}


/**
 * Log user activity
 */
function logActivity($userType, $userId, $action, $details = '') {
    try {
        $db = Database::getInstance();
        $stmt = $db->prepare("INSERT INTO activity_logs (user_type, user_id, action, details, ip_address, user_agent) VALUES (?, ?, ?, ?, ?, ?)");
        $stmt->execute([
            $userType,
            $userId,
            $action,
            $details,
            $_SERVER['REMOTE_ADDR'] ?? '',
            $_SERVER['HTTP_USER_AGENT'] ?? ''
        ]);
    } catch (Exception $e) {
        error_log("Failed to log activity: " . $e->getMessage());
    }
}

/**
 * Send email notification
 */
function sendEmail($to, $subject, $message, $isHTML = true) {
    // This is a placeholder for email functionality
    // You can integrate with PHPMailer or similar library
    return true;
}

/**
 * Calculate age from date of birth
 */
function calculateAge($dob) {
    $today = new DateTime();
    $birthDate = new DateTime($dob);
    return $today->diff($birthDate)->y;
}

/**
 * Get user's IP address
 */
function getUserIP() {
    if (!empty($_SERVER['HTTP_CLIENT_IP'])) {
        return $_SERVER['HTTP_CLIENT_IP'];
    } elseif (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
        return $_SERVER['HTTP_X_FORWARDED_FOR'];
    } else {
        return $_SERVER['REMOTE_ADDR'];
    }
}

/**
 * Time ago function
 */
function timeAgo($datetime) {
    $time = time() - strtotime($datetime);
    
    if ($time < 60) return 'just now';
    if ($time < 3600) return floor($time/60) . ' minutes ago';
    if ($time < 86400) return floor($time/3600) . ' hours ago';
    if ($time < 2592000) return floor($time/86400) . ' days ago';
    if ($time < 31536000) return floor($time/2592000) . ' months ago';
    
    return floor($time/31536000) . ' years ago';
}
?>
