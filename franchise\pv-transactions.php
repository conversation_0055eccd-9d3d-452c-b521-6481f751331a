<?php
/**
 * Franchise PV Transactions
 * MLM Binary Plan System
 */

require_once '../includes/header.php';
require_once '../includes/Auth.php';

// Require franchise authentication
if (!isLoggedIn('franchise')) {
    Response::redirect('login.php');
}

$currentUser = getCurrentUser();
$franchiseId = getCurrentUserId();

// Get franchise details
$db = Database::getInstance();
$franchiseStmt = $db->prepare("SELECT * FROM franchise WHERE id = ?");
$franchiseStmt->execute([$franchiseId]);
$franchiseDetails = $franchiseStmt->fetch();

// Get filter parameters
$userId = $_GET['user_id'] ?? '';
$side = $_GET['side'] ?? '';
$page = (int) ($_GET['page'] ?? 1);
$limit = 20;
$offset = ($page - 1) * $limit;

// Build query
$whereClause = "WHERE u.franchise_id = ?";
$params = [$franchiseId];

if ($userId) {
    $whereClause .= " AND pt.user_id = ?";
    $params[] = $userId;
}

if ($side) {
    $whereClause .= " AND pt.side = ?";
    $params[] = $side;
}

// Get PV transactions
$transactionsStmt = $db->prepare("
    SELECT pt.*, u.full_name, u.user_id as user_code, p.name as product_name, p.price
    FROM pv_transactions pt
    JOIN users u ON pt.user_id = u.user_id
    LEFT JOIN products p ON pt.product_id = p.id
    {$whereClause}
    ORDER BY pt.created_at DESC
    LIMIT ? OFFSET ?
");
$params[] = $limit;
$params[] = $offset;
$transactionsStmt->execute($params);
$transactions = $transactionsStmt->fetchAll();

// Get total count
$countParams = array_slice($params, 0, -2);
$countStmt = $db->prepare("
    SELECT COUNT(*) 
    FROM pv_transactions pt
    JOIN users u ON pt.user_id = u.user_id
    {$whereClause}
");
$countStmt->execute($countParams);
$totalTransactions = $countStmt->fetchColumn();
$totalPages = ceil($totalTransactions / $limit);

// Get franchise users for filter
$usersStmt = $db->prepare("SELECT user_id, full_name FROM users WHERE franchise_id = ? ORDER BY full_name");
$usersStmt->execute([$franchiseId]);
$users = $usersStmt->fetchAll();

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PV Transactions - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
    <style>
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 sidebar p-0">
                <div class="p-3">
                    <div class="text-center text-white mb-4">
                        <i class="fas fa-store fa-3x mb-2"></i>
                        <h5><?php echo htmlspecialchars($franchiseDetails['full_name']); ?></h5>
                        <small><?php echo htmlspecialchars($franchiseDetails['franchise_code']); ?></small>
                    </div>
                    
                    <nav class="nav flex-column">
                        <a class="nav-link text-white" href="dashboard.php">
                            <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                        </a>
                        <a class="nav-link text-white" href="users.php">
                            <i class="fas fa-users me-2"></i>Manage Users
                        </a>
                        <a class="nav-link text-white" href="products.php">
                            <i class="fas fa-box me-2"></i>Product Assignment
                        </a>
                        <a class="nav-link text-white active" href="pv-transactions.php">
                            <i class="fas fa-chart-line me-2"></i>PV Transactions
                        </a>
                        <a class="nav-link text-white" href="billing.php">
                            <i class="fas fa-file-invoice-dollar me-2"></i>Billing & Invoices
                        </a>
                        <a class="nav-link text-white" href="reports.php">
                            <i class="fas fa-chart-bar me-2"></i>Reports
                        </a>
                        <a class="nav-link text-white" href="profile.php">
                            <i class="fas fa-user me-2"></i>Profile
                        </a>
                        <hr class="text-white">
                        <a class="nav-link text-white" href="../logout.php">
                            <i class="fas fa-sign-out-alt me-2"></i>Logout
                        </a>
                    </nav>
                </div>
            </div>
            
            <!-- Main Content -->
            <div class="col-md-9 col-lg-10">
                <div class="p-4">
                    <!-- Header -->
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h2>PV Transactions</h2>
                        <a href="products.php" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>Assign Product
                        </a>
                    </div>
                    
                    <!-- Filters -->
                    <div class="card mb-4">
                        <div class="card-body">
                            <form method="GET" action="">
                                <div class="row">
                                    <div class="col-md-4 mb-3">
                                        <label for="user_id" class="form-label">User</label>
                                        <select class="form-select" id="user_id" name="user_id">
                                            <option value="">All Users</option>
                                            <?php foreach ($users as $user): ?>
                                                <option value="<?php echo htmlspecialchars($user['user_id']); ?>" 
                                                        <?php echo $userId === $user['user_id'] ? 'selected' : ''; ?>>
                                                    <?php echo htmlspecialchars($user['full_name']); ?> (<?php echo htmlspecialchars($user['user_id']); ?>)
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <label for="side" class="form-label">PV Side</label>
                                        <select class="form-select" id="side" name="side">
                                            <option value="">All Sides</option>
                                            <option value="left" <?php echo $side === 'left' ? 'selected' : ''; ?>>Left</option>
                                            <option value="right" <?php echo $side === 'right' ? 'selected' : ''; ?>>Right</option>
                                        </select>
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <label class="form-label">&nbsp;</label>
                                        <div>
                                            <button type="submit" class="btn btn-primary">
                                                <i class="fas fa-filter me-2"></i>Filter
                                            </button>
                                            <a href="pv-transactions.php" class="btn btn-outline-secondary">
                                                <i class="fas fa-times me-2"></i>Clear
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                    
                    <!-- Transactions Table -->
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">PV Transactions (<?php echo $totalTransactions; ?>)</h5>
                            <div class="text-muted">
                                Page <?php echo $page; ?> of <?php echo $totalPages; ?>
                            </div>
                        </div>
                        <div class="card-body">
                            <?php if (!empty($transactions)): ?>
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>User</th>
                                                <th>Product</th>
                                                <th>Type</th>
                                                <th>PV Amount</th>
                                                <th>Side</th>
                                                <th>Description</th>
                                                <th>Date</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($transactions as $transaction): ?>
                                                <tr>
                                                    <td>
                                                        <strong><?php echo htmlspecialchars($transaction['full_name']); ?></strong><br>
                                                        <small class="text-muted"><?php echo htmlspecialchars($transaction['user_code']); ?></small>
                                                    </td>
                                                    <td>
                                                        <?php if ($transaction['product_name']): ?>
                                                            <?php echo htmlspecialchars($transaction['product_name']); ?><br>
                                                            <small class="text-muted"><?php echo formatCurrency($transaction['price']); ?></small>
                                                        <?php else: ?>
                                                            <span class="text-muted">Manual Assignment</span>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td>
                                                        <span class="badge bg-<?php echo $transaction['transaction_type'] === 'purchase' ? 'success' : ($transaction['transaction_type'] === 'bonus' ? 'info' : 'warning'); ?>">
                                                            <?php echo ucfirst($transaction['transaction_type']); ?>
                                                        </span>
                                                    </td>
                                                    <td>
                                                        <span class="badge bg-primary fs-6"><?php echo formatPV($transaction['pv_amount']); ?></span>
                                                    </td>
                                                    <td>
                                                        <span class="badge bg-<?php echo $transaction['side'] === 'left' ? 'info' : 'warning'; ?>">
                                                            <?php echo ucfirst($transaction['side']); ?>
                                                        </span>
                                                    </td>
                                                    <td><?php echo htmlspecialchars($transaction['description']); ?></td>
                                                    <td><?php echo date('M d, Y H:i', strtotime($transaction['created_at'])); ?></td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                                
                                <!-- Pagination -->
                                <?php if ($totalPages > 1): ?>
                                    <nav class="mt-4">
                                        <ul class="pagination justify-content-center">
                                            <?php if ($page > 1): ?>
                                                <li class="page-item">
                                                    <a class="page-link" href="?page=<?php echo $page - 1; ?>&user_id=<?php echo urlencode($userId); ?>&side=<?php echo urlencode($side); ?>">Previous</a>
                                                </li>
                                            <?php endif; ?>
                                            
                                            <?php for ($i = max(1, $page - 2); $i <= min($totalPages, $page + 2); $i++): ?>
                                                <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                                                    <a class="page-link" href="?page=<?php echo $i; ?>&user_id=<?php echo urlencode($userId); ?>&side=<?php echo urlencode($side); ?>"><?php echo $i; ?></a>
                                                </li>
                                            <?php endfor; ?>
                                            
                                            <?php if ($page < $totalPages): ?>
                                                <li class="page-item">
                                                    <a class="page-link" href="?page=<?php echo $page + 1; ?>&user_id=<?php echo urlencode($userId); ?>&side=<?php echo urlencode($side); ?>">Next</a>
                                                </li>
                                            <?php endif; ?>
                                        </ul>
                                    </nav>
                                <?php endif; ?>
                                
                            <?php else: ?>
                                <div class="text-center py-4">
                                    <i class="fas fa-chart-line fa-3x text-muted mb-3"></i>
                                    <p class="text-muted">No PV transactions found</p>
                                    <a href="products.php" class="btn btn-primary">Assign First Product</a>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
