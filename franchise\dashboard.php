<?php
/**
 * Franchise Dashboard
 * MLM Binary Plan System
 */

require_once '../includes/header.php';
require_once '../includes/Auth.php';
require_once '../includes/PVSystem.php';
require_once '../includes/Wallet.php';

// Require franchise authentication
if (!isLoggedIn('franchise')) {
    Response::redirect('login.php');
}

$currentUser = getCurrentUser();
$franchiseId = getCurrentUserId();

// Get franchise details
$db = Database::getInstance();
$franchiseStmt = $db->prepare("SELECT * FROM franchise WHERE id = ?");
$franchiseStmt->execute([$franchiseId]);
$franchiseDetails = $franchiseStmt->fetch();

// Get franchise statistics
$userStats = $db->prepare("
    SELECT 
        COUNT(*) as total_users,
        SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active_users,
        SUM(CASE WHEN status = 'inactive' THEN 1 ELSE 0 END) as inactive_users,
        SUM(CASE WHEN DATE(registration_date) = CURDATE() THEN 1 ELSE 0 END) as today_registrations
    FROM users WHERE franchise_id = ?
");
$userStats->execute([$franchiseId]);
$userStatsData = $userStats->fetch();

// Get product statistics
$productStats = $db->query("
    SELECT 
        COUNT(*) as total_products,
        SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active_products
    FROM products
")->fetch();

// Get recent PV transactions for franchise users
$recentPVStmt = $db->prepare("
    SELECT pt.*, u.full_name, u.user_id as user_code, p.name as product_name
    FROM pv_transactions pt
    JOIN users u ON pt.user_id = u.user_id
    LEFT JOIN products p ON pt.product_id = p.id
    WHERE u.franchise_id = ? AND pt.created_by_type = 'franchise'
    ORDER BY pt.created_at DESC
    LIMIT 10
");
$recentPVStmt->execute([$franchiseId]);
$recentPV = $recentPVStmt->fetchAll();

// Get commission earnings (simplified calculation)
$commissionStmt = $db->prepare("
    SELECT 
        SUM(pt.pv_amount * ?) as total_commission
    FROM pv_transactions pt
    JOIN users u ON pt.user_id = u.user_id
    WHERE u.franchise_id = ? AND pt.created_by_type = 'franchise'
");
$commissionStmt->execute([$franchiseDetails['commission_rate'] / 100, $franchiseId]);
$commissionData = $commissionStmt->fetch();
$totalCommission = $commissionData['total_commission'] ?? 0;

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Franchise Dashboard - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
    <style>
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        }
        .stat-card {
            border-radius: 15px;
            border: none;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        .stat-card:hover {
            transform: translateY(-5px);
        }
        .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: white;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 sidebar p-0">
                <div class="p-3">
                    <div class="text-center text-white mb-4">
                        <i class="fas fa-store fa-3x mb-2"></i>
                        <h5><?php echo htmlspecialchars($franchiseDetails['full_name']); ?></h5>
                        <small><?php echo htmlspecialchars($franchiseDetails['franchise_code']); ?></small>
                    </div>
                    
                    <nav class="nav flex-column">
                        <a class="nav-link text-white active" href="dashboard.php">
                            <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                        </a>
                        <a class="nav-link text-white" href="users.php">
                            <i class="fas fa-users me-2"></i>Manage Users
                        </a>
                        <a class="nav-link text-white" href="products.php">
                            <i class="fas fa-box me-2"></i>Product Assignment
                        </a>
                        <a class="nav-link text-white" href="pv-transactions.php">
                            <i class="fas fa-chart-line me-2"></i>PV Transactions
                        </a>
                        <a class="nav-link text-white" href="billing.php">
                            <i class="fas fa-file-invoice-dollar me-2"></i>Billing & Invoices
                        </a>
                        <a class="nav-link text-white" href="reports.php">
                            <i class="fas fa-chart-bar me-2"></i>Reports
                        </a>
                        <a class="nav-link text-white" href="profile.php">
                            <i class="fas fa-user me-2"></i>Profile
                        </a>
                        <hr class="text-white">
                        <a class="nav-link text-white" href="../logout.php">
                            <i class="fas fa-sign-out-alt me-2"></i>Logout
                        </a>
                    </nav>
                </div>
            </div>
            
            <!-- Main Content -->
            <div class="col-md-9 col-lg-10">
                <div class="p-4">
                    <!-- Header -->
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h2>Franchise Dashboard</h2>
                        <div class="text-muted">
                            <i class="fas fa-calendar me-2"></i><?php echo date('F d, Y'); ?>
                        </div>
                    </div>
                    
                    <!-- Statistics Cards -->
                    <div class="row mb-4">
                        <div class="col-md-3 mb-3">
                            <div class="card stat-card">
                                <div class="card-body">
                                    <div class="d-flex align-items-center">
                                        <div class="stat-icon bg-primary me-3">
                                            <i class="fas fa-users"></i>
                                        </div>
                                        <div>
                                            <h3 class="mb-0"><?php echo $userStatsData['total_users']; ?></h3>
                                            <small class="text-muted">Total Users</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-3 mb-3">
                            <div class="card stat-card">
                                <div class="card-body">
                                    <div class="d-flex align-items-center">
                                        <div class="stat-icon bg-success me-3">
                                            <i class="fas fa-user-check"></i>
                                        </div>
                                        <div>
                                            <h3 class="mb-0"><?php echo $userStatsData['active_users']; ?></h3>
                                            <small class="text-muted">Active Users</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-3 mb-3">
                            <div class="card stat-card">
                                <div class="card-body">
                                    <div class="d-flex align-items-center">
                                        <div class="stat-icon bg-info me-3">
                                            <i class="fas fa-user-plus"></i>
                                        </div>
                                        <div>
                                            <h3 class="mb-0"><?php echo $userStatsData['today_registrations']; ?></h3>
                                            <small class="text-muted">Today's Registrations</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-3 mb-3">
                            <div class="card stat-card">
                                <div class="card-body">
                                    <div class="d-flex align-items-center">
                                        <div class="stat-icon bg-warning me-3">
                                            <i class="fas fa-coins"></i>
                                        </div>
                                        <div>
                                            <h3 class="mb-0"><?php echo formatCurrency($totalCommission); ?></h3>
                                            <small class="text-muted">Total Commission</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Quick Actions -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">Quick Actions</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-3 mb-2">
                                            <a href="users.php?action=add" class="btn btn-primary w-100">
                                                <i class="fas fa-user-plus me-2"></i>Add New User
                                            </a>
                                        </div>
                                        <div class="col-md-3 mb-2">
                                            <a href="products.php?action=assign" class="btn btn-success w-100">
                                                <i class="fas fa-box me-2"></i>Assign Product
                                            </a>
                                        </div>
                                        <div class="col-md-3 mb-2">
                                            <a href="pv-transactions.php" class="btn btn-info w-100">
                                                <i class="fas fa-chart-line me-2"></i>View PV Transactions
                                            </a>
                                        </div>
                                        <div class="col-md-3 mb-2">
                                            <a href="reports.php" class="btn btn-warning w-100">
                                                <i class="fas fa-chart-bar me-2"></i>Generate Reports
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Recent PV Transactions -->
                    <div class="row">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header d-flex justify-content-between align-items-center">
                                    <h5 class="mb-0">Recent PV Transactions</h5>
                                    <a href="pv-transactions.php" class="btn btn-sm btn-outline-primary">View All</a>
                                </div>
                                <div class="card-body">
                                    <?php if (!empty($recentPV)): ?>
                                        <div class="table-responsive">
                                            <table class="table table-hover">
                                                <thead>
                                                    <tr>
                                                        <th>User</th>
                                                        <th>Product</th>
                                                        <th>PV Amount</th>
                                                        <th>Side</th>
                                                        <th>Date</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <?php foreach ($recentPV as $pv): ?>
                                                        <tr>
                                                            <td>
                                                                <strong><?php echo htmlspecialchars($pv['full_name']); ?></strong><br>
                                                                <small class="text-muted"><?php echo htmlspecialchars($pv['user_code']); ?></small>
                                                            </td>
                                                            <td><?php echo htmlspecialchars($pv['product_name'] ?? 'Manual Assignment'); ?></td>
                                                            <td><span class="badge bg-primary"><?php echo formatPV($pv['pv_amount']); ?></span></td>
                                                            <td>
                                                                <span class="badge bg-<?php echo $pv['side'] === 'left' ? 'info' : 'warning'; ?>">
                                                                    <?php echo ucfirst($pv['side']); ?>
                                                                </span>
                                                            </td>
                                                            <td><?php echo date('M d, Y H:i', strtotime($pv['created_at'])); ?></td>
                                                        </tr>
                                                    <?php endforeach; ?>
                                                </tbody>
                                            </table>
                                        </div>
                                    <?php else: ?>
                                        <div class="text-center py-4">
                                            <i class="fas fa-chart-line fa-3x text-muted mb-3"></i>
                                            <p class="text-muted">No PV transactions yet</p>
                                            <a href="products.php?action=assign" class="btn btn-primary">Assign First Product</a>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
